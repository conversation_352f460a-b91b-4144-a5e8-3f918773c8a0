from rest_framework import viewsets, status, permissions
from rest_framework.response import Response
from rest_framework.decorators import action
from django.shortcuts import get_object_or_404
from django.db import transaction
from django.utils import timezone
from django_tenants.utils import schema_context

from workflows.models import CitizenClearanceRequest
from tenants.serializers.clearance_serializers import (
    CitizenClearanceRequestSerializer,
    CitizenClearanceRequestCreateSerializer,
    CitizenClearanceReviewSerializer,
    CitizenClearanceDocumentUploadSerializer
)
from tenants.models import Tenant
from users.models import User


class CitizenClearanceViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing citizen clearance requests.
    
    Workflow:
    1. POST /clearances/ - Kebele leader creates clearance request
    2. GET /clearances/ - List clearances (filtered by user's role and kebele)
    3. POST /clearances/{id}/review/ - Subcity admin approves/rejects
    4. POST /clearances/{id}/issue/ - Issue clearance letter
    5. POST /clearances/{id}/cancel/ - Cancel clearance request
    """
    
    serializer_class = CitizenClearanceRequestSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Filter clearances based on user role and tenant."""
        user = self.request.user
        
        if user.is_superuser:
            # Superadmin can see all clearances
            return CitizenClearanceRequest.objects.all()
        
        if not hasattr(user, 'tenant') or not user.tenant:
            return CitizenClearanceRequest.objects.none()
        
        tenant = user.tenant
        
        if tenant.type == 'kebele':
            # Kebele users can see clearances from their kebele
            return CitizenClearanceRequest.objects.filter(source_kebele=tenant)
        
        elif tenant.type == 'subcity':
            # Subcity users can see clearances from their child kebeles
            child_kebeles = Tenant.objects.filter(parent=tenant, type='kebele')
            return CitizenClearanceRequest.objects.filter(source_kebele__in=child_kebeles)
        
        elif tenant.type == 'city':
            # City users can see all clearances in their city
            city_kebeles = Tenant.objects.filter(
                parent__parent=tenant,
                type='kebele'
            )
            return CitizenClearanceRequest.objects.filter(source_kebele__in=city_kebeles)
        
        return CitizenClearanceRequest.objects.none()
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'create':
            return CitizenClearanceRequestCreateSerializer
        elif self.action == 'review':
            return CitizenClearanceReviewSerializer
        elif self.action == 'upload_documents':
            return CitizenClearanceDocumentUploadSerializer
        return CitizenClearanceRequestSerializer
    
    def create(self, request, *args, **kwargs):
        """Create clearance request with proper validation."""
        print(f"🔍 CLEARANCE CREATE DEBUG: Method called")
        print(f"🔍 User: {request.user}")
        print(f"🔍 User authenticated: {request.user.is_authenticated}")
        print(f"🔍 User tenant: {getattr(request.user, 'tenant', 'NO_TENANT')}")
        print(f"🔍 User role: {getattr(request.user, 'role', 'NO_ROLE')}")

        user = request.user

        # Validate user has permission to create clearance requests
        if not user.tenant or user.tenant.type != 'kebele':
            return Response(
                {'error': 'Only kebele users can create clearance requests'},
                status=status.HTTP_403_FORBIDDEN
            )

        if user.role not in ['kebele_leader', 'clerk']:
            return Response(
                {'error': 'Only kebele leaders and clerks can create clearance requests'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Check for existing pending clearance for this citizen
        citizen_id = request.data.get('citizen_id')
        if citizen_id:
            existing_clearance = CitizenClearanceRequest.objects.filter(
                source_kebele=user.tenant,
                citizen_id=citizen_id,
                status__in=['pending', 'approved', 'issued']
            ).first()

            if existing_clearance:
                return Response(
                    {'error': 'There is already a pending clearance request for this citizen'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Create the clearance request and immediately issue it (simplified workflow)
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        try:
            with transaction.atomic():
                # Save with additional fields - immediately issued
                clearance_request = serializer.save(
                    source_kebele=user.tenant,
                    requested_by=user,
                    status='issued',  # Skip approval workflow
                    reviewed_by=user,  # Auto-approved by creator
                    issued_by=user,
                    reviewed_at=timezone.now(),
                    issued_at=timezone.now(),
                    review_notes='Direct issuance - no approval required'
                )

                # Generate clearance letter immediately
                clearance_letter_path = self._generate_clearance_letter(clearance_request)
                clearance_request.clearance_letter_path = clearance_letter_path
                clearance_request.save()

                # Return response with clearance letter info
                response_data = CitizenClearanceRequestSerializer(clearance_request).data
                response_data['clearance_letter_path'] = clearance_letter_path

                from django.conf import settings
                response_data['clearance_letter_url'] = f"{settings.MEDIA_URL}{clearance_letter_path}"

                return Response({
                    'message': 'Clearance letter created and issued successfully',
                    'clearance': response_data
                }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response(
                {'error': f'Failed to create clearance letter: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def review(self, request, pk=None):
        """Review clearance request (approve/reject) - Subcity admin only."""
        clearance_request = self.get_object()
        user = request.user
        
        # Check permissions
        if not user.tenant or user.tenant.type != 'subcity':
            return Response(
                {'error': 'Only subcity admins can review clearance requests'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Check if user has clearance approval permission (subcity_admin by default, or custom permission)
        has_clearance_permission = (
            user.role == 'subcity_admin' or
            user.has_custom_permission('approve_clearance_requests') or
            user.has_group_permission('approve_clearance_requests')
        )

        if not has_clearance_permission:
            return Response(
                {'error': 'You do not have permission to review clearance requests'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Check if clearance is from a kebele under this subcity
        if clearance_request.source_kebele.parent != user.tenant:
            return Response(
                {'error': 'You can only review clearances from your kebeles'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Check if clearance can be reviewed
        if not clearance_request.can_be_approved and not clearance_request.can_be_rejected:
            return Response(
                {'error': 'This clearance request cannot be reviewed'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        serializer = self.get_serializer(clearance_request, data=request.data)
        serializer.is_valid(raise_exception=True)
        
        action = serializer.validated_data['action']
        review_notes = serializer.validated_data.get('review_notes', '')
        
        with transaction.atomic():
            if action == 'approve':
                clearance_request.status = 'approved'
            else:  # reject
                clearance_request.status = 'rejected'
            
            clearance_request.reviewed_by = user
            clearance_request.reviewed_at = timezone.now()
            clearance_request.review_notes = review_notes
            clearance_request.save()
        
        return Response({
            'message': f'Clearance request {action}d successfully',
            'clearance': CitizenClearanceRequestSerializer(clearance_request).data
        })
    
    @action(detail=True, methods=['post'])
    def issue(self, request, pk=None):
        """Issue clearance letter - Kebele leader only."""
        clearance_request = self.get_object()
        user = request.user
        
        # Check permissions
        if not user.tenant or user.tenant.type != 'kebele':
            return Response(
                {'error': 'Only kebele users can issue clearance letters'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        if user.role not in ['kebele_leader', 'clerk']:
            return Response(
                {'error': 'Only kebele leaders and clerks can issue clearance letters'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Check if this is the source kebele
        if clearance_request.source_kebele != user.tenant:
            return Response(
                {'error': 'You can only issue clearance letters for your kebele'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Check if clearance can be issued
        if not clearance_request.can_be_issued:
            return Response(
                {'error': 'This clearance request cannot be issued (must be approved first)'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        with transaction.atomic():
            # Generate clearance letter
            clearance_letter_path = self._generate_clearance_letter(clearance_request)

            clearance_request.status = 'issued'
            clearance_request.issued_by = user
            clearance_request.issued_at = timezone.now()
            clearance_request.clearance_letter_path = clearance_letter_path
            clearance_request.save()
        
        return Response({
            'message': 'Clearance letter issued successfully',
            'clearance': CitizenClearanceRequestSerializer(clearance_request).data,
            'clearance_letter_path': clearance_letter_path
        })
    
    @action(detail=True, methods=['post'])
    def upload_documents(self, request, pk=None):
        """Upload required documents for clearance request."""
        clearance_request = self.get_object()
        user = request.user
        
        # Check permissions
        if clearance_request.source_kebele != user.tenant:
            return Response(
                {'error': 'You can only upload documents for your kebele clearances'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        serializer = self.get_serializer(clearance_request, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        
        with transaction.atomic():
            serializer.save(documents_uploaded_at=timezone.now())
        
        return Response({
            'message': 'Documents uploaded successfully',
            'clearance': CitizenClearanceRequestSerializer(clearance_request).data
        })
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """Cancel clearance request."""
        clearance_request = self.get_object()
        user = request.user
        
        # Check permissions
        if clearance_request.source_kebele != user.tenant:
            return Response(
                {'error': 'You can only cancel your kebele clearances'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        if not clearance_request.can_be_cancelled:
            return Response(
                {'error': 'This clearance request cannot be cancelled'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        with transaction.atomic():
            clearance_request.status = 'cancelled'
            clearance_request.save()
        
        return Response({
            'message': 'Clearance request cancelled successfully',
            'clearance': CitizenClearanceRequestSerializer(clearance_request).data
        })
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get clearance statistics for the user's tenant."""
        queryset = self.get_queryset()

        stats = {
            'total': queryset.count(),
            'pending': queryset.filter(status='pending').count(),
            'approved': queryset.filter(status='approved').count(),
            'rejected': queryset.filter(status='rejected').count(),
            'issued': queryset.filter(status='issued').count(),
            'cancelled': queryset.filter(status='cancelled').count(),
        }

        return Response(stats)

    @action(detail=False, methods=['get', 'post'])
    def test(self, request):
        """Test endpoint to debug permission issues."""
        print(f"🔍 TEST ENDPOINT REACHED!")
        print(f"🔍 Method: {request.method}")
        print(f"🔍 User: {request.user}")
        print(f"🔍 User authenticated: {request.user.is_authenticated}")

        return Response({
            'message': 'Test endpoint reached successfully',
            'method': request.method,
            'user': str(request.user),
            'authenticated': request.user.is_authenticated
        })

    @action(detail=False, methods=['post'])
    def generate(self, request):
        """Generate clearance letter directly without approval workflow."""
        user = request.user

        # Validate user has permission to generate clearance letters
        if not user.tenant or user.tenant.type != 'kebele':
            return Response(
                {'error': 'Only kebele users can generate clearance letters'},
                status=status.HTTP_403_FORBIDDEN
            )

        if user.role not in ['kebele_leader', 'clerk']:
            return Response(
                {'error': 'Only kebele leaders and clerks can generate clearance letters'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Validate required data
        required_fields = ['citizen_id', 'citizen_name', 'destination_location', 'clearance_reason', 'reason_description']
        for field in required_fields:
            if not request.data.get(field):
                return Response(
                    {'error': f'{field.replace("_", " ").title()} is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        try:
            with transaction.atomic():
                # Create clearance record for tracking
                clearance_request = CitizenClearanceRequest.objects.create(
                    citizen_id=request.data['citizen_id'],
                    citizen_name=request.data['citizen_name'],
                    citizen_digital_id=request.data.get('citizen_digital_id', ''),
                    source_kebele=user.tenant,
                    destination_location=request.data['destination_location'],
                    clearance_reason=request.data['clearance_reason'],
                    reason_description=request.data['reason_description'],
                    status='issued',  # Skip approval workflow
                    requested_by=user,
                    reviewed_by=user,  # Auto-approved by issuer
                    issued_by=user,
                    reviewed_at=timezone.now(),
                    issued_at=timezone.now()
                )

                # Generate clearance letter
                clearance_letter_path = self._generate_clearance_letter(clearance_request)
                clearance_request.clearance_letter_path = clearance_letter_path
                clearance_request.save()

                # Return response with download URL
                from django.conf import settings
                clearance_letter_url = f"{settings.MEDIA_URL}{clearance_letter_path}"

                return Response({
                    'message': 'Clearance letter generated successfully',
                    'clearance_id': clearance_request.clearance_id,
                    'clearance_letter_path': clearance_letter_path,
                    'clearance_letter_url': clearance_letter_url,
                    'citizen_name': clearance_request.citizen_name,
                    'destination_location': clearance_request.destination_location
                })

        except Exception as e:
            return Response(
                {'error': f'Failed to generate clearance letter: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def auto_approve(self, request, pk=None):
        """Auto-approve clearance request and generate letter (simplified workflow)."""
        clearance_request = self.get_object()
        user = request.user

        # Check permissions
        if not user.tenant or user.tenant.type != 'kebele':
            return Response(
                {'error': 'Only kebele users can auto-approve clearance requests'},
                status=status.HTTP_403_FORBIDDEN
            )

        if user.role not in ['kebele_leader', 'clerk']:
            return Response(
                {'error': 'Only kebele leaders and clerks can auto-approve clearance requests'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Check if this is the source kebele
        if clearance_request.source_kebele != user.tenant:
            return Response(
                {'error': 'You can only auto-approve clearance requests for your kebele'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Check if clearance can be auto-approved
        if clearance_request.status != 'pending':
            return Response(
                {'error': 'This clearance request cannot be auto-approved'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            with transaction.atomic():
                # Auto-approve and generate letter
                clearance_request.status = 'issued'
                clearance_request.reviewed_by = user
                clearance_request.issued_by = user
                clearance_request.reviewed_at = timezone.now()
                clearance_request.issued_at = timezone.now()
                clearance_request.review_notes = 'Auto-approved via simplified workflow'

                # Generate clearance letter
                clearance_letter_path = self._generate_clearance_letter(clearance_request)
                clearance_request.clearance_letter_path = clearance_letter_path
                clearance_request.save()

                # Return response with download URL
                from django.conf import settings
                clearance_letter_url = f"{settings.MEDIA_URL}{clearance_letter_path}"

                return Response({
                    'message': 'Clearance request auto-approved and letter generated successfully',
                    'clearance': CitizenClearanceRequestSerializer(clearance_request).data,
                    'clearance_letter_url': clearance_letter_url
                })

        except Exception as e:
            return Response(
                {'error': f'Failed to auto-approve clearance request: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _generate_clearance_letter(self, clearance_request):
        """Generate clearance letter PDF (placeholder implementation)."""
        import os
        from django.conf import settings

        # Create clearance letters directory if it doesn't exist
        clearance_dir = os.path.join(settings.MEDIA_ROOT, 'clearance_letters')
        os.makedirs(clearance_dir, exist_ok=True)

        # Generate professional PDF clearance letter using ReportLab
        from django.utils import timezone
        from reportlab.lib.pagesizes import A4
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT

        # Generate filename
        filename = f"{clearance_request.clearance_id}_clearance_letter.pdf"
        file_path = os.path.join(clearance_dir, filename)

        # Create PDF document
        doc = SimpleDocTemplate(
            file_path,
            pagesize=A4,
            rightMargin=0.75*inch,
            leftMargin=0.75*inch,
            topMargin=1*inch,
            bottomMargin=1*inch
        )

        # Get styles
        styles = getSampleStyleSheet()

        # Custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.darkblue,
            fontName='Helvetica-Bold'
        )

        header_style = ParagraphStyle(
            'CustomHeader',
            parent=styles['Heading2'],
            fontSize=14,
            spaceAfter=20,
            alignment=TA_CENTER,
            textColor=colors.darkred,
            fontName='Helvetica-Bold'
        )

        body_style = ParagraphStyle(
            'CustomBody',
            parent=styles['Normal'],
            fontSize=12,
            spaceAfter=12,
            alignment=TA_LEFT,
            fontName='Helvetica'
        )

        signature_style = ParagraphStyle(
            'SignatureStyle',
            parent=styles['Normal'],
            fontSize=11,
            spaceAfter=8,
            alignment=TA_LEFT,
            fontName='Helvetica'
        )

        # Build content
        story = []

        # Government Header
        gov_header = f"""
        <para align=center>
        <b>FEDERAL DEMOCRATIC REPUBLIC OF ETHIOPIA</b><br/>
        <b>{clearance_request.source_kebele.parent.parent.name.upper() if clearance_request.source_kebele.parent and clearance_request.source_kebele.parent.parent else 'CITY ADMINISTRATION'}</b><br/>
        <b>{clearance_request.source_kebele.parent.name.upper() if clearance_request.source_kebele.parent else 'SUBCITY ADMINISTRATION'}</b><br/>
        <b>{clearance_request.source_kebele.name.upper()}</b>
        </para>
        """
        story.append(Paragraph(gov_header, header_style))
        story.append(Spacer(1, 20))

        # Title
        story.append(Paragraph("CITIZEN CLEARANCE LETTER", title_style))
        story.append(Spacer(1, 20))

        # Document info table
        doc_info_data = [
            ['Clearance ID:', clearance_request.clearance_id],
            ['Date of Issue:', timezone.now().strftime('%B %d, %Y')],
            ['Reference No:', f"CL-{clearance_request.clearance_id}-{timezone.now().year}"]
        ]

        doc_info_table = Table(doc_info_data, colWidths=[2*inch, 3*inch])
        doc_info_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
        ]))
        story.append(doc_info_table)
        story.append(Spacer(1, 30))

        # Main content
        story.append(Paragraph("<b>TO WHOM IT MAY CONCERN:</b>", body_style))
        story.append(Spacer(1, 15))

        main_content = f"""
        This is to certify that <b>{clearance_request.citizen_name}</b>
        (Digital ID: <b>{clearance_request.citizen_digital_id or 'N/A'}</b>)
        has been officially cleared to relocate from <b>{clearance_request.source_kebele.name}</b>
        to <b>{clearance_request.destination_location}</b>.
        """
        story.append(Paragraph(main_content, body_style))
        story.append(Spacer(1, 15))

        # Reason details
        reason_content = f"""
        <b>Reason for clearance:</b> {clearance_request.get_clearance_reason_display()}<br/>
        <b>Details:</b> {clearance_request.reason_description}
        """
        story.append(Paragraph(reason_content, body_style))
        story.append(Spacer(1, 15))

        # Timeline
        timeline_content = f"""
        This clearance was requested on <b>{clearance_request.created_at.strftime('%B %d, %Y')}</b>
        and approved on <b>{clearance_request.reviewed_at.strftime('%B %d, %Y') if clearance_request.reviewed_at else 'N/A'}</b>.
        """
        story.append(Paragraph(timeline_content, body_style))
        story.append(Spacer(1, 15))

        # Certification statement
        cert_content = """
        The citizen is in good standing and has no pending obligations
        with the local administration. This clearance letter is issued
        in accordance with the applicable laws and regulations.
        """
        story.append(Paragraph(cert_content, body_style))
        story.append(Spacer(1, 40))

        # Signature section
        signature_data = [
            ['', ''],
            ['Issued by:', ''],
            [f'{clearance_request.issued_by.first_name} {clearance_request.issued_by.last_name}', ''],
            [f'Position: {clearance_request.issued_by.role.replace("_", " ").title()}', ''],
            [f'Date: {timezone.now().strftime("%B %d, %Y")}', ''],
            ['', ''],
            ['_________________________', 'OFFICIAL SEAL'],
            ['Signature', ''],
        ]

        signature_table = Table(signature_data, colWidths=[3*inch, 2*inch])
        signature_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('FONTNAME', (0, 1), (0, 4), 'Helvetica-Bold'),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('ALIGN', (1, 6), (1, 7), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('FONTSIZE', (1, 6), (1, 7), 10),
            ('FONTNAME', (1, 6), (1, 6), 'Helvetica-Bold'),
        ]))
        story.append(signature_table)
        story.append(Spacer(1, 30))

        # Footer
        footer_content = f"""
        <para align=center>
        <i>This document is computer generated and does not require a physical signature when accompanied by official seal.</i><br/>
        <b>{clearance_request.source_kebele.name}</b> |
        <b>{clearance_request.source_kebele.parent.name if clearance_request.source_kebele.parent else ''}</b> |
        <b>{clearance_request.source_kebele.parent.parent.name if clearance_request.source_kebele.parent and clearance_request.source_kebele.parent.parent else ''}</b>
        </para>
        """
        story.append(Paragraph(footer_content, signature_style))

        # Build PDF
        doc.build(story)

        # Return relative path for storage in database
        return f"clearance_letters/{filename}"
