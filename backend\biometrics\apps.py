from django.apps import AppConfig


class BiometricsConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'biometrics'
    verbose_name = 'Biometrics'

    def ready(self):
        """Initialize biometric device connections when app starts"""
        try:
            from .device_integration import FutronicFS88H
            # Initialize device manager
            device_manager = FutronicFS88H()
            device_manager.initialize()
        except Exception as e:
            # Log error but don't crash the app
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Failed to initialize biometric device: {e}")
