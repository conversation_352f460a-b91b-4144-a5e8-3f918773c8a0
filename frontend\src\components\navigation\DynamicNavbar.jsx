import { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  AppBar,
  Box,
  Toolbar,
  IconButton,
  Typography,
  Menu,
  Container,
  Avatar,
  Button,
  Tooltip,
  MenuItem,
  Divider,
  ListItemIcon,
  useMediaQuery,
  useTheme,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Collapse,
  Badge,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Person as PersonIcon,
  CreditCard as CardIcon,
  Business as BusinessIcon,
  Brightness4 as DarkModeIcon,
  Brightness7 as LightModeIcon,
  AccountCircle,
  Settings,
  Logout,
  Assessment as ReportIcon,
  SupervisorAccount as SupervisorIcon,
  Timeline as TimelineIcon,
  Badge as BadgeIcon,
  ExpandLess,
  ExpandMore,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme as useAppTheme } from '../../contexts/ThemeContext';
import { usePermissions } from '../../hooks/usePermissions';

// Icon mapping for dynamic navigation
const ICON_MAP = {
  Dashboard: DashboardIcon,
  People: PeopleIcon,
  Person: PersonIcon,
  Badge: BadgeIcon,
  Business: BusinessIcon,
  Assessment: ReportIcon,
  SupervisorAccount: SupervisorIcon,
  Timeline: TimelineIcon,
  Settings: Settings,
};

const DynamicNavbar = () => {
  const { user, logout } = useAuth();
  const { mode, toggleTheme } = useAppTheme();
  const { getDynamicNavigationItems, userPermissions, userGroups } = usePermissions();
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Check if this is a public page
  const isPublicPage = location.pathname === '/services' ||
                       location.pathname.startsWith('/services/') ||
                       location.pathname.startsWith('/idcards/services') ||
                       !user;

  const [mobileOpen, setMobileOpen] = useState(false);
  const [anchorElUser, setAnchorElUser] = useState(null);
  const [expandedItems, setExpandedItems] = useState({});

  // Get dynamic navigation items based on user's actual permissions
  const navigationItems = !isPublicPage ? getDynamicNavigationItems() : [];

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleOpenUserMenu = (event) => {
    setAnchorElUser(event.currentTarget);
  };

  const handleCloseUserMenu = () => {
    setAnchorElUser(null);
  };

  const handleLogout = () => {
    logout();
    navigate('/login');
    handleCloseUserMenu();
  };

  const handleProfileClick = () => {
    navigate('/profile');
    handleCloseUserMenu();
  };

  const handleExpandClick = (itemId) => {
    setExpandedItems(prev => ({
      ...prev,
      [itemId]: !prev[itemId]
    }));
  };

  const renderNavigationItem = (item, isMobile = false) => {
    const IconComponent = ICON_MAP[item.icon] || DashboardIcon;
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems[item.id];

    if (isMobile) {
      return (
        <Box key={item.id}>
          <ListItem disablePadding>
            <ListItemButton
              onClick={() => {
                if (hasChildren) {
                  handleExpandClick(item.id);
                } else {
                  navigate(item.path);
                  handleDrawerToggle();
                }
              }}
            >
              <ListItemIcon>
                <IconComponent />
              </ListItemIcon>
              <ListItemText primary={item.label} />
              {hasChildren && (isExpanded ? <ExpandLess /> : <ExpandMore />)}
            </ListItemButton>
          </ListItem>
          {hasChildren && (
            <Collapse in={isExpanded} timeout="auto" unmountOnExit>
              <List component="div" disablePadding>
                {item.children.map((child) => (
                  <ListItem key={child.id} disablePadding sx={{ pl: 4 }}>
                    <ListItemButton
                      onClick={() => {
                        navigate(child.path);
                        handleDrawerToggle();
                      }}
                    >
                      <ListItemText primary={child.label} />
                    </ListItemButton>
                  </ListItem>
                ))}
              </List>
            </Collapse>
          )}
        </Box>
      );
    }

    // Desktop rendering
    if (hasChildren) {
      return (
        <Box key={item.id}>
          <Button
            onClick={() => navigate(item.path)}
            sx={{
              my: 2,
              color: 'white',
              display: 'flex',
              alignItems: 'center',
              textTransform: 'none',
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
              }
            }}
            startIcon={<IconComponent />}
          >
            {item.label}
          </Button>
        </Box>
      );
    }

    return (
      <Button
        key={item.id}
        onClick={() => navigate(item.path)}
        sx={{
          my: 2,
          color: 'white',
          display: 'flex',
          alignItems: 'center',
          textTransform: 'none',
          '&:hover': {
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
          }
        }}
        startIcon={<IconComponent />}
      >
        {item.label}
      </Button>
    );
  };

  const drawer = (
    <Box onClick={() => {}} sx={{ textAlign: 'center' }}>
      <Typography variant="h6" sx={{ my: 2, fontWeight: 'bold' }}>
        {user?.tenant_name || 'GoID'}
      </Typography>
      <Divider />
      
      {/* User Info */}
      <Box sx={{ p: 2, bgcolor: 'grey.100' }}>
        <Typography variant="body2" color="text.secondary">
          {user?.username}
        </Typography>
        <Typography variant="caption" color="text.secondary">
          {user?.role} • {userGroups.length} groups
        </Typography>
        <Typography variant="caption" display="block" color="text.secondary">
          {userPermissions.length} permissions
        </Typography>
      </Box>
      
      <List>
        {navigationItems.map((item) => renderNavigationItem(item, true))}
      </List>
    </Box>
  );

  return (
    <>
      <AppBar position="fixed" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}>
        <Container maxWidth="xl">
          <Toolbar disableGutters>
            {/* Logo - Desktop */}
            <Typography
              variant="h6"
              noWrap
              component="div"
              sx={{
                mr: 2,
                display: { xs: 'none', md: 'flex' },
                fontWeight: 'bold',
                color: 'inherit',
                cursor: 'pointer',
              }}
              onClick={() => navigate('/dashboard')}
            >
              {user?.tenant_name || 'GoID'}
            </Typography>

            {/* Mobile menu icon */}
            {!isPublicPage && (
              <Box sx={{ flexGrow: 0, display: { xs: 'flex', md: 'none' } }}>
                <IconButton
                  size="large"
                  aria-label="menu"
                  onClick={handleDrawerToggle}
                  color="inherit"
                >
                  <MenuIcon />
                </IconButton>
              </Box>
            )}

            {/* Logo - Mobile */}
            <Typography
              variant="h6"
              noWrap
              component="div"
              sx={{
                flexGrow: 1,
                display: { xs: 'flex', md: 'none' },
                fontWeight: 'bold',
              }}
              onClick={() => navigate('/dashboard')}
            >
              {user?.tenant_name || 'GoID'}
            </Typography>

            {/* Desktop navigation */}
            <Box sx={{ flexGrow: 1, display: { xs: 'none', md: 'flex' } }}>
              {navigationItems.map((item) => renderNavigationItem(item, false))}
            </Box>

            {/* User info badge */}
            {!isPublicPage && (
              <Box sx={{ display: { xs: 'none', sm: 'flex' }, alignItems: 'center', mr: 2 }}>
                <Typography variant="body2" sx={{ mr: 1 }}>
                  {user?.tenant_name}
                </Typography>
                <Badge badgeContent={userPermissions.length} color="secondary" max={99}>
                  <SupervisorIcon />
                </Badge>
              </Box>
            )}

            {/* Theme toggle */}
            <Tooltip title={`Switch to ${mode === 'light' ? 'dark' : 'light'} mode`}>
              <IconButton color="inherit" onClick={toggleTheme} sx={{ mr: 1 }}>
                {mode === 'light' ? <DarkModeIcon /> : <LightModeIcon />}
              </IconButton>
            </Tooltip>

            {/* User menu */}
            {!isPublicPage ? (
              <Box sx={{ flexGrow: 0 }}>
                <Tooltip title="Open settings">
                  <IconButton onClick={handleOpenUserMenu} sx={{ p: 0 }}>
                    <Avatar
                      alt={user?.username || 'User'}
                      src={user?.profile_picture || ''}
                      sx={{ bgcolor: 'secondary.main' }}
                    >
                      {user?.username ? user.username.charAt(0).toUpperCase() : 'U'}
                    </Avatar>
                  </IconButton>
                </Tooltip>
                <Menu
                  sx={{ mt: '45px' }}
                  anchorEl={anchorElUser}
                  anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
                  keepMounted
                  transformOrigin={{ vertical: 'top', horizontal: 'right' }}
                  open={Boolean(anchorElUser)}
                  onClose={handleCloseUserMenu}
                >
                  <MenuItem onClick={handleProfileClick}>
                    <ListItemIcon>
                      <AccountCircle fontSize="small" />
                    </ListItemIcon>
                    Profile
                  </MenuItem>
                  <MenuItem onClick={handleCloseUserMenu}>
                    <ListItemIcon>
                      <Settings fontSize="small" />
                    </ListItemIcon>
                    Settings
                  </MenuItem>
                  <Divider />
                  <MenuItem onClick={handleLogout}>
                    <ListItemIcon>
                      <Logout fontSize="small" />
                    </ListItemIcon>
                    Logout
                  </MenuItem>
                </Menu>
              </Box>
            ) : (
              <Button
                variant="contained"
                color="secondary"
                onClick={() => navigate('/login')}
                sx={{ ml: 2 }}
              >
                Login
              </Button>
            )}
          </Toolbar>
        </Container>
      </AppBar>

      {/* Mobile drawer */}
      <Drawer
        variant="temporary"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{ keepMounted: true }}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': { boxSizing: 'border-box', width: 280 },
        }}
      >
        {drawer}
      </Drawer>
    </>
  );
};

export default DynamicNavbar;
