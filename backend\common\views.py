"""
Language switching views for GoID system
"""

from django.http import JsonResponse
from django.utils import translation
from django.utils.translation import gettext as _
from django.conf import settings
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
import json


@api_view(['GET'])
def get_available_languages(request):
    """Get list of available languages"""
    
    languages = []
    for code, name in settings.LANGUAGES:
        languages.append({
            'code': code,
            'name': name,
            'is_current': code == translation.get_language()
        })
    
    return Response({
        'languages': languages,
        'current_language': translation.get_language()
    })


@api_view(['POST'])
def set_language(request):
    """Set user's preferred language"""
    
    try:
        data = json.loads(request.body) if request.body else {}
        language_code = data.get('language', 'en')
        
        # Validate language code
        available_languages = [code for code, name in settings.LANGUAGES]
        if language_code not in available_languages:
            return Response({
                'error': _('Invalid language code'),
                'available_languages': available_languages
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Activate the language
        translation.activate(language_code)
        
        # Store in session if available
        if hasattr(request, 'session'):
            request.session[translation.LANGUAGE_SESSION_KEY] = language_code
        
        return Response({
            'message': _('Language changed successfully'),
            'language': language_code,
            'language_name': dict(settings.LANGUAGES).get(language_code, language_code)
        })
        
    except json.JSONDecodeError:
        return Response({
            'error': _('Invalid JSON data')
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({
            'error': _('Failed to change language'),
            'details': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def get_translations(request):
    """Get translations for current language"""
    
    current_language = translation.get_language()
    
    # Basic translations that are commonly used in the frontend
    translations = {
        # Navigation
        'dashboard': _('Dashboard'),
        'citizens': _('Citizens'),
        'id_cards': _('ID Cards'),
        'clearance': _('Clearance'),
        'transfer': _('Transfer'),
        'print_queue': _('Print Queue'),
        'service_requests': _('Service Requests'),
        'kebele_users': _('Kebele Users'),
        'reports': _('Reports'),
        'citizen_directory': _('Citizen Directory'),
        'subcity_users': _('Subcity Users'),
        'tenants': _('Tenants'),
        'system_users': _('System Users'),
        'system_settings': _('System Settings'),
        
        # Common UI elements
        'profile': _('Profile'),
        'settings': _('Settings'),
        'logout': _('Logout'),
        'login': _('Login'),
        'welcome': _('Welcome'),
        'loading': _('Loading'),
        'save': _('Save'),
        'cancel': _('Cancel'),
        'delete': _('Delete'),
        'edit': _('Edit'),
        'add': _('Add'),
        'search': _('Search'),
        'filter': _('Filter'),
        'export': _('Export'),
        'import': _('Import'),
        'print': _('Print'),
        'refresh': _('Refresh'),
        
        # Page subtitles
        'dashboard_welcome': _('Welcome to your ID management dashboard'),
        'citizens_subtitle': _('Register and manage citizen information'),
        'id_cards_subtitle': _('Manage ID card printing and distribution'),
        'clearance_subtitle': _('Process clearance requests'),
        'transfer_subtitle': _('Handle citizen transfers between kebeles'),
        'print_queue_subtitle': _('Monitor ID card printing queue'),
        'service_requests_subtitle': _('Review and process service requests'),
        'kebele_users_subtitle': _('Manage kebele staff accounts'),
        'reports_subtitle': _('Generate and view system reports'),
        'citizen_directory_subtitle': _('Search and view all citizens'),
        'subcity_users_subtitle': _('Manage subcity staff accounts'),
        'tenants_subtitle': _('Manage system tenants and organizations'),
        'system_users_subtitle': _('Manage system administrators'),
        'system_settings_subtitle': _('Configure system-wide settings'),
        
        # Status messages
        'success': _('Success'),
        'error': _('Error'),
        'warning': _('Warning'),
        'info': _('Information'),
        
        # Form labels
        'first_name': _('First Name'),
        'last_name': _('Last Name'),
        'email': _('Email'),
        'phone': _('Phone'),
        'address': _('Address'),
        'date_of_birth': _('Date of Birth'),
        'gender': _('Gender'),
        'nationality': _('Nationality'),
        
        # Actions
        'create': _('Create'),
        'update': _('Update'),
        'view': _('View'),
        'download': _('Download'),
        'upload': _('Upload'),
        'submit': _('Submit'),
        'approve': _('Approve'),
        'reject': _('Reject'),
        'pending': _('Pending'),
        'completed': _('Completed'),
        'active': _('Active'),
        'inactive': _('Inactive'),
        
        # Biometric
        'fingerprint': _('Fingerprint'),
        'capture': _('Capture'),
        'verify': _('Verify'),
        'biometric_data': _('Biometric Data'),
        'left_thumb': _('Left Thumb'),
        'right_thumb': _('Right Thumb'),
        
        # Time
        'today': _('Today'),
        'yesterday': _('Yesterday'),
        'this_week': _('This Week'),
        'this_month': _('This Month'),
        'this_year': _('This Year'),
        
        # Pagination
        'previous': _('Previous'),
        'next': _('Next'),
        'first': _('First'),
        'last': _('Last'),
        'page': _('Page'),
        'of': _('of'),
        'showing': _('Showing'),
        'entries': _('entries'),
        
        # Confirmation
        'are_you_sure': _('Are you sure?'),
        'confirm_delete': _('Are you sure you want to delete this item?'),
        'confirm_action': _('Are you sure you want to perform this action?'),
        'yes': _('Yes'),
        'no': _('No'),
        'ok': _('OK'),
        
        # Validation
        'required_field': _('This field is required'),
        'invalid_email': _('Please enter a valid email address'),
        'invalid_phone': _('Please enter a valid phone number'),
        'password_mismatch': _('Passwords do not match'),
        'weak_password': _('Password is too weak'),
        
        # File operations
        'file_upload': _('File Upload'),
        'choose_file': _('Choose File'),
        'file_selected': _('File Selected'),
        'upload_success': _('File uploaded successfully'),
        'upload_error': _('File upload failed'),
        'invalid_file_type': _('Invalid file type'),
        'file_too_large': _('File is too large'),
        
        # Language
        'language': _('Language'),
        'english': _('English'),
        'amharic': _('Amharic'),
        'change_language': _('Change Language'),
    }
    
    return Response({
        'language': current_language,
        'translations': translations
    })


@api_view(['GET'])
def get_language_info(request):
    """Get current language information"""
    
    current_language = translation.get_language()
    language_name = dict(settings.LANGUAGES).get(current_language, current_language)
    
    return Response({
        'current_language': current_language,
        'language_name': language_name,
        'available_languages': [
            {
                'code': code,
                'name': name,
                'is_current': code == current_language
            }
            for code, name in settings.LANGUAGES
        ],
        'is_rtl': current_language in getattr(settings, 'LANGUAGES_RTL', [])
    })
