/**
 * Localization Service for GoID System
 * Handles language switching and translation management
 */

import axios from '../utils/axios';

class LocalizationService {
  constructor() {
    this.currentLanguage = localStorage.getItem('goid_language') || 'en';
    this.translations = {};
    this.availableLanguages = [
      { code: 'en', name: 'English', nativeName: 'English' },
      { code: 'am', name: '<PERSON>haric', nativeName: 'አማርኛ' }
    ];
    
    // Initialize with default translations
    this.initializeDefaultTranslations();
  }

  /**
   * Initialize default translations for offline use
   */
  initializeDefaultTranslations() {
    this.translations = {
      en: {
        // Navigation
        dashboard: 'Dashboard',
        citizens: 'Citizens',
        id_cards: 'ID Cards',
        clearance: 'Clearance',
        transfer: 'Transfer',
        print_queue: 'Print Queue',
        service_requests: 'Service Requests',
        kebele_users: 'Kebele Users',
        reports: 'Reports',
        citizen_directory: 'Citizen Directory',
        subcity_users: 'Subcity Users',
        tenants: 'Tenants',
        system_users: 'System Users',
        system_settings: 'System Settings',
        
        // Authentication
        login: 'Login',
        logout: 'Logout',
        username: 'Userna<PERSON>',
        password: 'Password',
        email: 'Em<PERSON>',
        first_name: 'First Name',
        last_name: 'Last Name',
        
        // Citizen Management
        add_citizen: 'Add Citizen',
        edit_citizen: 'Edit Citizen',
        delete_citizen: 'Delete Citizen',
        citizen_registration: 'Citizen Registration',
        full_name: 'Full Name',
        date_of_birth: 'Date of Birth',
        gender: 'Gender',
        male: 'Male',
        female: 'Female',
        phone_number: 'Phone Number',
        address: 'Address',
        nationality: 'Nationality',
        ethiopian: 'Ethiopian',
        
        // ID Card Management
        generate_id_card: 'Generate ID Card',
        print_id_card: 'Print ID Card',
        id_card_status: 'ID Card Status',
        pending: 'Pending',
        approved: 'Approved',
        rejected: 'Rejected',
        printed: 'Printed',
        
        // Biometric Capture
        capture_fingerprint: 'Capture Fingerprint',
        left_thumb: 'Left Thumb',
        right_thumb: 'Right Thumb',
        fingerprint_quality: 'Fingerprint Quality',
        high_quality: 'High Quality',
        medium_quality: 'Medium Quality',
        low_quality: 'Low Quality',
        biometric_data: 'Biometric Data',
        duplicate_detection: 'Duplicate Detection',
        no_duplicates_found: 'No Duplicates Found',
        duplicate_found: 'Duplicate Found',
        
        // Common Actions
        save: 'Save',
        cancel: 'Cancel',
        submit: 'Submit',
        edit: 'Edit',
        delete: 'Delete',
        view: 'View',
        search: 'Search',
        filter: 'Filter',
        export: 'Export',
        import: 'Import',
        print: 'Print',
        
        // Status Messages
        success: 'Success',
        error: 'Error',
        warning: 'Warning',
        information: 'Information',
        loading: 'Loading...',
        please_wait: 'Please wait...',
        
        // Form Validation
        field_required: 'This field is required.',
        invalid_email: 'Please enter a valid email address.',
        invalid_phone: 'Please enter a valid phone number.',
        password_min_length: 'Password must be at least 8 characters long.',
        
        // Language Settings
        language: 'Language',
        change_language: 'Change Language',
        language_changed: 'Language changed successfully',
        
        // System Messages
        system_ready: 'System is ready',
        connection_established: 'Connection established',
        connection_failed: 'Connection failed',
        device_connected: 'Device connected',
        device_not_found: 'Device not found'
      },
      
      am: {
        // Navigation
        dashboard: 'ዳሽቦርድ',
        citizens: 'ዜጎች',
        id_cards: 'መታወቂያ ካርዶች',
        clearance: 'ፍቃድ',
        transfer: 'ዝውውር',
        print_queue: 'የህትመት ወረፋ',
        service_requests: 'የአገልግሎት ጥያቄዎች',
        kebele_users: 'የቀበሌ ተጠቃሚዎች',
        reports: 'ሪፖርቶች',
        citizen_directory: 'የዜጎች ማውጫ',
        subcity_users: 'የንዑስ ከተማ ተጠቃሚዎች',
        tenants: 'ተከራዮች',
        system_users: 'የስርዓት ተጠቃሚዎች',
        system_settings: 'የስርዓት ቅንብሮች',
        
        // Authentication
        login: 'ግባ',
        logout: 'ውጣ',
        username: 'የተጠቃሚ ስም',
        password: 'የይለፍ ቃል',
        email: 'ኢሜይል',
        first_name: 'ስም',
        last_name: 'የአባት ስም',
        
        // Citizen Management
        add_citizen: 'ዜጋ ጨምር',
        edit_citizen: 'ዜጋን አርም',
        delete_citizen: 'ዜጋን ሰርዝ',
        citizen_registration: 'የዜጋ ምዝገባ',
        full_name: 'ሙሉ ስም',
        date_of_birth: 'የተወለደበት ቀን',
        gender: 'ጾታ',
        male: 'ወንድ',
        female: 'ሴት',
        phone_number: 'ስልክ ቁጥር',
        address: 'አድራሻ',
        nationality: 'ዜግነት',
        ethiopian: 'ኢትዮጵያዊ',
        
        // ID Card Management
        generate_id_card: 'መታወቂያ ካርድ ፍጠር',
        print_id_card: 'መታወቂያ ካርድ አትም',
        id_card_status: 'የመታወቂያ ካርድ ሁኔታ',
        pending: 'በመጠባበቅ ላይ',
        approved: 'ጸድቋል',
        rejected: 'ተቀባይነት አላገኘም',
        printed: 'ታትሟል',
        
        // Biometric Capture
        capture_fingerprint: 'የጣት አሻራ ያንሱ',
        left_thumb: 'የግራ አውራ ጣት',
        right_thumb: 'የቀኝ አውራ ጣት',
        fingerprint_quality: 'የጣት አሻራ ጥራት',
        high_quality: 'ከፍተኛ ጥራት',
        medium_quality: 'መካከለኛ ጥራት',
        low_quality: 'ዝቅተኛ ጥራት',
        biometric_data: 'ባዮሜትሪክ መረጃ',
        duplicate_detection: 'ተደጋጋሚ ፍለጋ',
        no_duplicates_found: 'ተደጋጋሚ አልተገኘም',
        duplicate_found: 'ተደጋጋሚ ተገኝቷል',
        
        // Common Actions
        save: 'አስቀምጥ',
        cancel: 'ሰርዝ',
        submit: 'አስገባ',
        edit: 'አርም',
        delete: 'ሰርዝ',
        view: 'ይመልከቱ',
        search: 'ፈልግ',
        filter: 'ማጣሪያ',
        export: 'ወደ ውጭ ላክ',
        import: 'ከውጭ አምጣ',
        print: 'አትም',
        
        // Status Messages
        success: 'ተሳክቷል',
        error: 'ስህተት',
        warning: 'ማስጠንቀቂያ',
        information: 'መረጃ',
        loading: 'በመጫን ላይ...',
        please_wait: 'እባክዎ ይጠብቁ...',
        
        // Form Validation
        field_required: 'ይህ መስክ አስፈላጊ ነው።',
        invalid_email: 'እባክዎ ትክክለኛ ኢሜይል አድራሻ ያስገቡ።',
        invalid_phone: 'እባክዎ ትክክለኛ ስልክ ቁጥር ያስገቡ።',
        password_min_length: 'የይለፍ ቃል ቢያንስ 8 ቁምፊዎች ሊኖሩት ይገባል።',
        
        // Language Settings
        language: 'ቋንቋ',
        change_language: 'ቋንቋ ቀይር',
        language_changed: 'ቋንቋ በተሳካ ሁኔታ ተቀይሯል',
        
        // System Messages
        system_ready: 'ስርዓቱ ዝግጁ ነው',
        connection_established: 'ግንኙነት ተመስርቷል',
        connection_failed: 'ግንኙነት አልተሳካም',
        device_connected: 'መሳሪያ ተገናኝቷል',
        device_not_found: 'መሳሪያ አልተገኘም'
      }
    };
  }

  /**
   * Get current language
   */
  getCurrentLanguage() {
    return this.currentLanguage;
  }

  /**
   * Get available languages
   */
  getAvailableLanguages() {
    return this.availableLanguages;
  }

  /**
   * Set language
   */
  async setLanguage(languageCode) {
    try {
      // Update backend
      await axios.post('/api/common/language/set/', {
        language: languageCode
      });

      // Update local state
      this.currentLanguage = languageCode;
      localStorage.setItem('goid_language', languageCode);

      // Load translations from backend
      await this.loadTranslations();

      return true;
    } catch (error) {
      console.error('Failed to set language:', error);
      
      // Fallback to local language change
      this.currentLanguage = languageCode;
      localStorage.setItem('goid_language', languageCode);
      
      return false;
    }
  }

  /**
   * Load translations from backend
   */
  async loadTranslations() {
    try {
      const response = await axios.get('/api/common/translations/');
      
      if (response.data && response.data.translations) {
        // Merge backend translations with local ones
        this.translations[this.currentLanguage] = {
          ...this.translations[this.currentLanguage],
          ...response.data.translations
        };
      }
    } catch (error) {
      console.warn('Failed to load translations from backend, using local translations:', error);
    }
  }

  /**
   * Get translation for a key
   */
  t(key, defaultValue = null) {
    const languageTranslations = this.translations[this.currentLanguage] || this.translations['en'];
    return languageTranslations[key] || defaultValue || key;
  }

  /**
   * Get translation with parameters
   */
  tp(key, params = {}, defaultValue = null) {
    let translation = this.t(key, defaultValue);
    
    // Replace parameters in translation
    Object.keys(params).forEach(param => {
      translation = translation.replace(`{${param}}`, params[param]);
    });
    
    return translation;
  }

  /**
   * Check if current language is RTL
   */
  isRTL() {
    return ['ar', 'he', 'fa'].includes(this.currentLanguage);
  }

  /**
   * Get language direction
   */
  getDirection() {
    return this.isRTL() ? 'rtl' : 'ltr';
  }

  /**
   * Initialize localization service
   */
  async initialize() {
    try {
      // Load current language info from backend
      const response = await axios.get('/api/common/language/info/');
      
      if (response.data && response.data.current_language) {
        this.currentLanguage = response.data.current_language;
        localStorage.setItem('goid_language', this.currentLanguage);
      }
      
      // Load translations
      await this.loadTranslations();
      
      return true;
    } catch (error) {
      console.warn('Failed to initialize localization from backend, using local settings:', error);
      return false;
    }
  }
}

// Create singleton instance
const localizationService = new LocalizationService();

export default localizationService;
