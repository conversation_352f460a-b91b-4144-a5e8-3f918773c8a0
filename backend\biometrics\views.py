"""
Biometrics API views for fingerprint device integration.
"""

import logging
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views.decorators.http import require_http_methods

from .device_integration import get_device, FingerprintTemplate
from .fingerprint_processing import FingerprintProcessor
from tenants.permissions import CanManageTenants

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def device_status(request):
    """
    Get fingerprint device status.
    
    Returns device connection status, model info, and last error.
    """
    try:
        device = get_device()
        status_info = device.get_device_status()
        
        return Response({
            'success': True,
            'data': status_info
        })
        
    except Exception as e:
        logger.error(f"Error getting device status: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def initialize_device(request):
    """
    Initialize the fingerprint device.
    
    Attempts to connect and initialize the Futronic FS88H device.
    """
    try:
        device = get_device()
        success = device.initialize()
        
        if success:
            return Response({
                'success': True,
                'message': 'Device initialized successfully',
                'data': device.get_device_status()
            })
        else:
            return Response({
                'success': False,
                'error': device.last_error or 'Device initialization failed'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
    except Exception as e:
        logger.error(f"Error initializing device: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def capture_fingerprint(request):
    """
    Capture a fingerprint from the device.
    
    Expected payload:
    {
        "thumb_type": "left" | "right"
    }
    """
    try:
        thumb_type = request.data.get('thumb_type')
        
        if not thumb_type or thumb_type not in ['left', 'right']:
            return Response({
                'success': False,
                'error': 'thumb_type must be "left" or "right"'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        logger.info(f"🔍 GoID backend capturing {thumb_type} thumb fingerprint...")
        device = get_device()
        logger.info(f"📱 Using device: {device.__class__.__name__}")
        template = device.capture_fingerprint(thumb_type)
        
        if template:
            # Validate quality using processor
            processor = FingerprintProcessor()
            quality_result = processor.validate_template_quality(template.template_data)

            # Ensure all data is properly formatted for frontend
            template_data = template.template_data
            if not isinstance(template_data, str):
                template_data = str(template_data)

            return Response({
                'success': True,
                'data': {
                    'thumb_type': str(template.thumb_type),
                    'template_data': template_data,  # Ensure this is a string
                    'quality_score': int(template.quality_score),
                    'quality_valid': quality_result.get('is_valid', True),
                    'quality_message': quality_result.get('message', f'Quality score: {template.quality_score}%'),
                    'minutiae_count': int(template.minutiae_count),
                    'capture_time': str(template.capture_time),
                    'device_info': template.device_info
                }
            })
        else:
            return Response({
                'success': False,
                'error': 'Failed to capture fingerprint'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
    except Exception as e:
        logger.error(f"Error capturing fingerprint: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated, CanManageTenants])
def process_fingerprint(request):
    """
    Process raw fingerprint data into a template.
    
    Expected payload:
    {
        "raw_data": "base64_encoded_data",
        "thumb_type": "left" | "right"
    }
    """
    try:
        raw_data = request.data.get('raw_data')
        thumb_type = request.data.get('thumb_type')
        
        if not raw_data:
            return Response({
                'success': False,
                'error': 'raw_data is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if not thumb_type or thumb_type not in ['left', 'right']:
            return Response({
                'success': False,
                'error': 'thumb_type must be "left" or "right"'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        device = get_device()
        template = device.process_template(raw_data, thumb_type)
        
        return Response({
            'success': True,
            'data': {
                'thumb_type': template.thumb_type,
                'template_data': template.template_data,
                'quality_score': template.quality_score,
                'minutiae_count': template.minutiae_count,
                'capture_time': template.capture_time,
                'device_info': template.device_info
            }
        })
        
    except Exception as e:
        logger.error(f"Error processing fingerprint: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def validate_quality(request):
    """
    Validate fingerprint template quality.
    
    Expected payload:
    {
        "fingerprint_data": "base64_encoded_template"
    }
    """
    try:
        fingerprint_data = request.data.get('fingerprint_data')
        
        if not fingerprint_data:
            return Response({
                'success': False,
                'error': 'fingerprint_data is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        processor = FingerprintProcessor()
        quality_result = processor.validate_template_quality(fingerprint_data)
        
        return Response({
            'success': True,
            'data': quality_result
        })
        
    except Exception as e:
        logger.error(f"Error validating fingerprint quality: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated, CanManageTenants])
def match_fingerprints(request):
    """
    Match two fingerprint templates.
    
    Expected payload:
    {
        "template1": "base64_encoded_template",
        "template2": "base64_encoded_template"
    }
    """
    try:
        template1 = request.data.get('template1')
        template2 = request.data.get('template2')
        
        if not template1 or not template2:
            return Response({
                'success': False,
                'error': 'Both template1 and template2 are required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        device = get_device()
        is_match, confidence = device.match_templates(template1, template2)
        
        return Response({
            'success': True,
            'data': {
                'is_match': is_match,
                'confidence_score': confidence,
                'match_threshold': 0.8
            }
        })
        
    except Exception as e:
        logger.error(f"Error matching fingerprints: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def check_duplicates(request):
    """
    Check for duplicate fingerprints across tenants.
    
    Expected payload:
    {
        "left_thumb_fingerprint": "base64_template",
        "right_thumb_fingerprint": "base64_template"
    }
    """
    try:
        left_thumb = request.data.get('left_thumb_fingerprint')
        right_thumb = request.data.get('right_thumb_fingerprint')
        
        if not left_thumb and not right_thumb:
            return Response({
                'success': False,
                'error': 'At least one fingerprint template is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        processor = FingerprintProcessor()
        duplicate_result = processor.check_duplicates({
            'left_thumb_fingerprint': left_thumb,
            'right_thumb_fingerprint': right_thumb
        })
        
        return Response({
            'success': True,
            'data': duplicate_result
        })
        
    except Exception as e:
        logger.error(f"Error checking duplicates: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def fraud_prevention_check(request):
    """
    Comprehensive fraud prevention check for citizen registration.

    Expected payload:
    {
        "citizen_data": {
            "first_name": "John",
            "last_name": "Doe",
            "date_of_birth": "1990-01-01",
            "phone": "+251911000000",
            "email": "<EMAIL>"
        },
        "biometric_data": {
            "left_thumb_fingerprint": "base64_template",
            "right_thumb_fingerprint": "base64_template"
        }
    }
    """
    try:
        citizen_data = request.data.get('citizen_data', {})
        biometric_data = request.data.get('biometric_data', {})

        if not citizen_data and not biometric_data:
            return Response({
                'success': False,
                'error': 'Either citizen_data or biometric_data is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        fraud_results = {
            'risk_level': 'low',
            'fraud_detected': False,
            'indicators': [],
            'matches': [],
            'biometric_duplicates': [],
            'personal_duplicates': [],
            'recommendations': []
        }

        # Check biometric duplicates if provided
        if biometric_data.get('left_thumb_fingerprint') or biometric_data.get('right_thumb_fingerprint'):
            processor = FingerprintProcessor()
            duplicate_result = processor.check_duplicates(biometric_data)

            if duplicate_result['has_duplicates']:
                fraud_results['fraud_detected'] = True
                fraud_results['risk_level'] = 'critical'
                fraud_results['indicators'].append('Duplicate fingerprints detected')
                fraud_results['biometric_duplicates'] = duplicate_result['matches']
                fraud_results['matches'].extend(duplicate_result['matches'])
                fraud_results['recommendations'].append(
                    'BLOCK REGISTRATION: Identical fingerprints found in system'
                )

        # Check personal information duplicates if provided
        if citizen_data:
            # Import here to avoid circular imports
            from idcards.fraud_detection import FraudDetectionService

            fraud_service = FraudDetectionService()
            personal_fraud = fraud_service.check_fraud_indicators(citizen_data, request.user.tenant_id)

            if personal_fraud['risk_level'] in ['high', 'critical']:
                if fraud_results['risk_level'] == 'low':
                    fraud_results['risk_level'] = personal_fraud['risk_level']
                fraud_results['indicators'].extend(personal_fraud['indicators'])
                fraud_results['personal_duplicates'] = personal_fraud.get('matches', [])
                fraud_results['matches'].extend(personal_fraud.get('matches', []))

                if personal_fraud['risk_level'] == 'critical':
                    fraud_results['fraud_detected'] = True
                    fraud_results['recommendations'].append(
                        'HIGH RISK: Similar personal information found in other kebeles'
                    )

        # Generate final recommendations
        if fraud_results['risk_level'] == 'critical':
            fraud_results['recommendations'].insert(0,
                '🚨 REGISTRATION BLOCKED: Critical fraud indicators detected'
            )
        elif fraud_results['risk_level'] == 'high':
            fraud_results['recommendations'].insert(0,
                '⚠️ HIGH RISK: Manual verification required before registration'
            )
        elif fraud_results['risk_level'] == 'medium':
            fraud_results['recommendations'].insert(0,
                '⚠️ MEDIUM RISK: Additional verification recommended'
            )
        else:
            fraud_results['recommendations'].append(
                '✅ LOW RISK: No significant fraud indicators detected'
            )

        return Response({
            'success': True,
            'data': fraud_results
        })

    except Exception as e:
        logger.error(f"Error in fraud prevention check: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated, CanManageTenants])
def biometric_stats(request):
    """
    Get biometric statistics for the current tenant.
    """
    try:
        processor = FingerprintProcessor()
        stats = processor.get_biometric_statistics()
        
        return Response({
            'success': True,
            'data': stats
        })
        
    except Exception as e:
        logger.error(f"Error getting biometric stats: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def test_duplicate_detection(request):
    """
    Test endpoint to verify duplicate detection is working.
    This will create test data and check if duplicates are detected.
    """
    try:
        # Create a test fingerprint template
        test_template = "test_fingerprint_template_12345"

        processor = FingerprintProcessor()

        # Test 1: Compare identical templates
        match1, score1 = processor._compare_templates(test_template, test_template)

        # Test 2: Compare different templates
        match2, score2 = processor._compare_templates(test_template, "different_template_67890")

        # Test 3: Check current biometric database
        from tenants.models.citizen import Biometric
        biometric_count = Biometric.objects.count()

        # Test 4: Run actual duplicate check with test data
        test_data = {
            'left_thumb_fingerprint': test_template,
            'right_thumb_fingerprint': test_template
        }
        duplicate_result = processor.check_duplicates(test_data)

        return Response({
            'success': True,
            'data': {
                'test_results': {
                    'identical_templates': {
                        'match': match1,
                        'score': score1,
                        'expected': 'Should be True with score 1.0'
                    },
                    'different_templates': {
                        'match': match2,
                        'score': score2,
                        'expected': 'Should be False with low score'
                    },
                    'database_check': {
                        'biometric_records_found': biometric_count,
                        'duplicate_check_result': duplicate_result
                    }
                },
                'processor_config': {
                    'match_threshold': processor.match_threshold,
                    'quality_threshold': processor.quality_threshold
                },
                'recommendations': [
                    'If identical_templates.match is False, the comparison algorithm needs fixing',
                    'If biometric_records_found > 0 but no duplicates detected, check real data comparison',
                    'Try registering the same citizen twice to test real duplicate detection'
                ]
            }
        })

    except Exception as e:
        logger.error(f"Error in test duplicate detection: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def debug_fingerprint_data(request):
    """
    Debug endpoint to examine what fingerprint data is actually stored in the database.
    """
    try:
        from tenants.models.citizen import Biometric, Citizen
        from tenants.models import Tenant
        from django_tenants.utils import schema_context
        import base64
        import json

        debug_info = {
            'current_tenant': request.user.tenant.name if hasattr(request.user, 'tenant') else 'Unknown',
            'tenants_checked': [],
            'biometric_records': [],
            'template_analysis': []
        }

        # Get all kebele tenants
        tenants = Tenant.objects.filter(type='kebele')

        for tenant in tenants:
            tenant_info = {
                'name': tenant.name,
                'schema': tenant.schema_name,
                'biometric_count': 0,
                'citizens': []
            }

            try:
                with schema_context(tenant.schema_name):
                    biometrics = Biometric.objects.select_related('citizen').all()[:5]  # Limit to first 5
                    tenant_info['biometric_count'] = Biometric.objects.count()

                    for bio in biometrics:
                        citizen_info = {
                            'citizen_name': f"{bio.citizen.first_name} {bio.citizen.last_name}",
                            'citizen_id': bio.citizen.digital_id,
                            'left_thumb_length': len(bio.left_thumb_fingerprint) if bio.left_thumb_fingerprint else 0,
                            'right_thumb_length': len(bio.right_thumb_fingerprint) if bio.right_thumb_fingerprint else 0,
                            'left_thumb_preview': bio.left_thumb_fingerprint[:100] + '...' if bio.left_thumb_fingerprint else None,
                            'right_thumb_preview': bio.right_thumb_fingerprint[:100] + '...' if bio.right_thumb_fingerprint else None
                        }

                        # Try to decode and analyze the template
                        if bio.left_thumb_fingerprint:
                            try:
                                decoded = json.loads(base64.b64decode(bio.left_thumb_fingerprint).decode())
                                citizen_info['left_template_analysis'] = {
                                    'version': decoded.get('version', 'Unknown'),
                                    'has_fingerprint_image': 'fingerprint_image' in decoded,
                                    'frame_size': decoded.get('frame_size', 0),
                                    'device_model': decoded.get('device_info', {}).get('model', 'Unknown'),
                                    'thumb_type': decoded.get('thumb_type', 'Unknown'),
                                    'capture_time': decoded.get('capture_time', 'Unknown')
                                }

                                if 'fingerprint_image' in decoded:
                                    image_data = base64.b64decode(decoded['fingerprint_image'])
                                    citizen_info['left_template_analysis']['fingerprint_image_size'] = len(image_data)
                                    citizen_info['left_template_analysis']['fingerprint_image_preview'] = image_data[:20].hex()

                            except Exception as e:
                                citizen_info['left_template_analysis'] = {'error': str(e)}

                        tenant_info['citizens'].append(citizen_info)

            except Exception as e:
                tenant_info['error'] = str(e)

            debug_info['tenants_checked'].append(tenant_info)

        return Response({
            'success': True,
            'data': debug_info
        })

    except Exception as e:
        logger.error(f"Error in debug fingerprint data: {e}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
