import uuid
from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from ..utils import Timestamp
from .city import Country, Region
from .subcity import SubCity
from .kebele import <PERSON>bele, Ketena

class Religion(Timestamp):
    name = models.CharField(max_length=100)
    is_active = models.BooleanField(default=True)  # Boolean field for activity status

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Religion"
        verbose_name_plural = "Religions"

class CitizenStatus(Timestamp):
    name = models.CharField(max_length=100)  # e.g., Citizen, Resident, Visitor, Foreign National
    is_active = models.BooleanField(default=True)  # Boolean field for activity status

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Citizen Status"
        verbose_name_plural = "Citizen Statuses"

class MaritalStatus(Timestamp):
    name = models.CharField(max_length=50)  # e.g., Single, Married, etc.
    is_active = models.BooleanField(default=True)  # Boolean field for activity status

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Marital Status"
        verbose_name_plural = "Marital Statuses"

class DocumentType(Timestamp):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)  # Boolean field for activity status

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Document Type"
        verbose_name_plural = "Document Types"

class EmploymentType(Timestamp):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)  # Boolean field for activity status

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Employment Type"
        verbose_name_plural = "Employment Types"

class Relationship(Timestamp):
    # Relationship model to store various types of relationships
    name = models.CharField(max_length=100, unique=True)
    # Optionally, a description field to explain what this relationship entails
    description = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Relationship"
        verbose_name_plural = "Relationships"

class CurrentStatus(Timestamp):
    name = models.CharField(max_length=50, unique=True, help_text="Current status of the citizen")

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Current Status"
        verbose_name_plural = "Current Statuses"

# Define the Citizen model first
class Citizen(Timestamp):
    # Keep auto-incrementing ID for backward compatibility
    # TODO: Add UUID field later for cross-tenant uniqueness
    digital_id = models.CharField(max_length=50, unique=True)

    # Name Fields
    first_name = models.CharField(max_length=100)
    middle_name = models.CharField(max_length=100, blank=True, null=True)
    last_name = models.CharField(max_length=100)
    first_name_am = models.CharField(max_length=100, blank=True, null=True)
    middle_name_am = models.CharField(max_length=100, blank=True, null=True)
    last_name_am = models.CharField(max_length=100, blank=True, null=True)

    # Personal Information
    date_of_birth = models.DateField(null=True, blank=True)
    gender = models.CharField(max_length=10, blank=True, null=True)
    photo = models.TextField(blank=True, null=True)  # Base64 encoded photo

    # Relations (using integer fields to match main model)
    religion = models.IntegerField(null=True, blank=True)
    subcity = models.IntegerField(null=True, blank=True)
    kebele = models.IntegerField(null=True, blank=True)
    ketena = models.IntegerField(null=True, blank=True)
    status = models.IntegerField(null=True, blank=True)

    # Address/Contact
    house_number = models.CharField(max_length=20, blank=True, null=True)
    phone = models.CharField(max_length=20, blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    nationality = models.IntegerField(null=True, blank=True)
    region = models.IntegerField(null=True, blank=True)

    # ID Info
    id_issue_date = models.DateField(null=True, blank=True)  # Added null=True for migration purposes
    id_expiry_date = models.DateField(null=True, blank=True)  # Added null=True for migration purposes

    # Employment Info
    employment = models.CharField(max_length=100, blank=True, null=True)
    employee_type = models.CharField(max_length=100, blank=True, null=True)
    organization_name = models.CharField(max_length=200, blank=True, null=True)

    # Marital / Family Info
    marital_status = models.IntegerField(null=True, blank=True)

    # Parents Relationship
    mother = models.ForeignKey('Parent', related_name='children_as_mother', on_delete=models.SET_NULL, null=True, blank=True)
    father = models.ForeignKey('Parent', related_name='children_as_father', on_delete=models.SET_NULL, null=True, blank=True)

    # Is Resident of City for Child, Spouse, and Emergency Contact
    is_resident = models.BooleanField(default=False, help_text="Indicates whether the citizen is a resident of the city.")

    # Current Status (Transferred, Not Alive, etc.)
    current_status = models.ForeignKey(CurrentStatus, on_delete=models.SET_NULL, null=True, blank=True)
    is_active = models.BooleanField(default=True)

    # Transfer tracking fields
    transfer_reason = models.CharField(max_length=255, blank=True, null=True, help_text="Reason for transfer if citizen was transferred")
    transfer_date = models.DateTimeField(blank=True, null=True, help_text="Date when citizen was transferred")

    def __str__(self):
        return f"{self.first_name} {self.middle_name or ''} {self.last_name} ({self.digital_id})"

    def clean(self):
        # Validate age is greater than 18
        if self.id_issue_date:
            today = timezone.now().date()
            min_birth_date = today - timedelta(days=18*365)  # Approximate 18 years
            if self.id_issue_date > min_birth_date:
                raise ValidationError("Citizen must be at least 18 years old.")

    def get_full_name(self):
        if self.middle_name:
            return f"{self.first_name} {self.middle_name} {self.last_name}"
        return f"{self.first_name} {self.last_name}"

    @property
    def full_name(self):
        """Property for easy access to full name"""
        return self.get_full_name()

    def get_directory_path(self):
        """Return the directory path for storing documents and photos"""
        return f"{self.get_full_name()}_{self.digital_id}"

    class Meta:
        verbose_name = "Citizen"
        verbose_name_plural = "Citizens"

def citizen_document_path(instance, filename):
    """Function to determine the upload path for citizen documents"""
    # Get the file extension
    ext = filename.split('.')[-1]
    # Return the path with the citizen's directory and a standardized filename with document type
    doc_type = instance.document_type.name.lower().replace(' ', '_')
    return f"citizen_documents/{instance.citizen.get_directory_path()}/{doc_type}.{ext}"

class Document(Timestamp):
    document_type = models.ForeignKey(DocumentType, on_delete=models.CASCADE)
    citizen = models.ForeignKey(Citizen, on_delete=models.CASCADE, related_name='documents')
    document_file = models.FileField(upload_to=citizen_document_path)
    issue_date = models.DateField(null=True, blank=True)
    expiry_date = models.DateField(null=True, blank=True)
    is_active = models.BooleanField(default=True)  # Boolean field for activity status

    def __str__(self):
        return f"{self.document_type.name} for {self.citizen.first_name} {self.citizen.last_name}"

    class Meta:
        verbose_name = "Document"
        verbose_name_plural = "Documents"

class Parent(Timestamp):
    citizen = models.ForeignKey(Citizen, on_delete=models.CASCADE, related_name='parents', null=True, blank=True)
    first_name = models.CharField(max_length=100)
    middle_name = models.CharField(max_length=100, blank=True, null=True)
    last_name = models.CharField(max_length=100)
    first_name_am = models.CharField(max_length=100, blank=True, null=True)
    middle_name_am = models.CharField(max_length=100, blank=True, null=True)
    last_name_am = models.CharField(max_length=100, blank=True, null=True)
    gender = models.CharField(max_length=10, choices=[('male', 'Male'), ('female', 'Female')], null=True, blank=True)
    phone = models.CharField(max_length=20, blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    is_resident = models.BooleanField(default=False, help_text="Indicates whether the parent is a resident of the city.")
    nationality = models.IntegerField(null=True, blank=True)
    linked_citizen = models.ForeignKey(Citizen, on_delete=models.SET_NULL, null=True, blank=True, related_name="as_parent")

    def __str__(self):
        return f"{self.first_name} {self.middle_name or ''} {self.last_name} ({self.gender})"

    class Meta:
        verbose_name = "Parent"
        verbose_name_plural = "Parents"

class Child(Timestamp):
    citizen = models.ForeignKey(Citizen, on_delete=models.CASCADE, related_name='children')
    first_name = models.CharField(max_length=100)
    middle_name = models.CharField(max_length=100, blank=True, null=True)
    last_name = models.CharField(max_length=100)
    first_name_am = models.CharField(max_length=100, blank=True, null=True)
    middle_name_am = models.CharField(max_length=100, blank=True, null=True)
    last_name_am = models.CharField(max_length=100, blank=True, null=True)
    date_of_birth = models.DateField()
    is_active = models.BooleanField(default=True)
    is_resident = models.BooleanField(default=False, help_text="Indicates whether the child is a resident of the city.")
    nationality = models.IntegerField(null=True, blank=True)
    linked_citizen = models.ForeignKey(Citizen, on_delete=models.SET_NULL, null=True, blank=True, related_name="as_child")

    def __str__(self):
        return f"{self.first_name} {self.last_name} (Child of {self.citizen.digital_id})"

    class Meta:
        verbose_name = "Child"
        verbose_name_plural = "Children"

class EmergencyContact(Timestamp):
    citizen = models.ForeignKey(Citizen, on_delete=models.CASCADE, related_name="emergency_contacts")
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    middle_name = models.CharField(max_length=100, blank=True, null=True)
    relationship = models.CharField(max_length=100, blank=True, null=True)  # Changed from Country to CharField
    phone = models.CharField(max_length=20, blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    nationality = models.IntegerField(null=True, blank=True)
    primary_contact = models.BooleanField(default=False, help_text="Indicates whether this is the primary emergency contact.")
    is_active = models.BooleanField(default=True)
    is_resident = models.BooleanField(default=False, help_text="Indicates whether the emergency contact is a resident of the city.")
    linked_citizen = models.ForeignKey(Citizen, on_delete=models.SET_NULL, null=True, blank=True, related_name="as_emergency_contact")

    def clean(self):
        if not self.phone and not self.email:
            raise ValidationError("At least one contact method (phone or email) must be provided.")

    def __str__(self):
        return f"{self.first_name} {self.last_name} - {self.citizen.first_name} {self.citizen.last_name}"

    class Meta:
        verbose_name = "Emergency Contact"
        verbose_name_plural = "Emergency Contacts"
        unique_together = ('citizen', 'phone')  # Ensure no duplicate phone number for the same citizen

class Spouse(Timestamp):
    citizen = models.ForeignKey(Citizen, on_delete=models.CASCADE, related_name="spouse_records")
    first_name = models.CharField(max_length=100)
    middle_name = models.CharField(max_length=100, blank=True, null=True)
    last_name = models.CharField(max_length=100)
    phone = models.CharField(max_length=20, blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    nationality = models.IntegerField(null=True, blank=True)
    primary_contact = models.BooleanField(default=False, help_text="Indicates whether this is the primary emergency contact.")
    is_active = models.BooleanField(default=True)
    is_resident = models.BooleanField(default=False, help_text="Indicates whether the spouse is a resident of the city.")
    linked_citizen = models.ForeignKey(Citizen, on_delete=models.SET_NULL, null=True, blank=True, related_name="as_spouse")

    def clean(self):
        if not self.phone and not self.email:
            raise ValidationError("At least one contact method (phone or email) must be provided.")

    def __str__(self):
        return f"{self.first_name} {self.last_name} - {self.citizen.first_name} {self.citizen.last_name}"

    class Meta:
        verbose_name = "Spouse"
        verbose_name_plural = "Spouses"
        unique_together = ('citizen', 'phone')  # Ensure no duplicate phone number for the same citizen

def citizen_photo_path(instance, filename):
    """Function to determine the upload path for citizen photos"""
    # Get the file extension
    ext = filename.split('.')[-1]
    # Return the path with the citizen's directory and a standardized filename
    return f"citizen_photos/{instance.citizen.get_directory_path()}/photo.{ext}"

class Photo(Timestamp):
    citizen = models.OneToOneField(Citizen, on_delete=models.CASCADE, related_name="photo_record")
    photo = models.ImageField(upload_to=citizen_photo_path)
    upload_date = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Photo of {self.citizen.first_name} {self.citizen.last_name}"

    class Meta:
        verbose_name = "Photo"
        verbose_name_plural = "Photos"

class Biometric(Timestamp):
    citizen = models.OneToOneField(Citizen, on_delete=models.CASCADE, related_name="biometric_record")
    left_hand_fingerprint = models.BinaryField(blank=True, null=True)  # Store encoded left hand fingerprint
    right_hand_fingerprint = models.BinaryField(blank=True, null=True)  # Store encoded right hand fingerprint
    left_thumb_fingerprint = models.TextField(blank=True, null=True)  # Store encoded left thumb fingerprint template
    right_thumb_fingerprint = models.TextField(blank=True, null=True)  # Store encoded right thumb fingerprint template
    left_eye_iris_scan = models.BinaryField(blank=True, null=True)  # Store encoded left eye iris scan
    right_eye_iris_scan = models.BinaryField(blank=True, null=True)  # Store encoded right eye iris scan

    def __str__(self):
        return f"Biometric data for {self.citizen.first_name} {self.citizen.last_name}"

    class Meta:
        verbose_name = "Biometric"
        verbose_name_plural = "Biometrics"
