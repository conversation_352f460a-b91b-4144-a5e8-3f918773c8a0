"""
Futronic FS88H fingerprint device integration.
This module handles communication with the Futronic FS88H fingerprint scanner.
"""

import logging
import json
import base64
import time
from typing import Dict, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class FingerprintTemplate:
    """Data class for fingerprint template"""
    thumb_type: str  # 'left' or 'right'
    template_data: str  # Base64 encoded template
    quality_score: int  # 0-100
    minutiae_count: int
    capture_time: str
    device_info: Dict


@dataclass
class DeviceInfo:
    """Device information"""
    model: str = "Futronic FS88H"
    serial_number: str = "FS88H001"
    firmware_version: str = "1.2.3"
    sdk_version: str = "4.2.1"
    is_connected: bool = False


class FutronicFS88H:
    """
    Futronic FS88H fingerprint device integration class.
    
    This class provides methods to:
    - Initialize and connect to the device
    - Capture fingerprints
    - Process fingerprint templates
    - Validate fingerprint quality
    - Handle device errors
    """
    
    def __init__(self):
        self.device_info = DeviceInfo()
        self.is_initialized = False
        self.last_error = None
        
        # Quality thresholds
        self.min_quality_score = 75
        self.min_minutiae_count = 30
        
        # Timeout settings (in seconds)
        self.capture_timeout = 10
        self.processing_timeout = 5
    
    def initialize(self) -> bool:
        """
        Initialize the Futronic FS88H device.
        
        Returns:
            bool: True if initialization successful, False otherwise
        """
        try:
            logger.info("Initializing Futronic FS88H device...")
            
            # In a real implementation, this would:
            # 1. Load the Futronic SDK
            # 2. Initialize the device driver
            # 3. Check device connection
            # 4. Get device information
            
            # Mock implementation for development
            self.device_info.is_connected = True
            self.is_initialized = True
            
            logger.info(f"Device initialized successfully: {self.device_info.model}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize device: {e}")
            self.last_error = str(e)
            return False
    
    def get_device_status(self) -> Dict:
        """
        Get current device status.
        
        Returns:
            Dict: Device status information
        """
        return {
            'connected': self.device_info.is_connected,
            'initialized': self.is_initialized,
            'model': self.device_info.model,
            'serial_number': self.device_info.serial_number,
            'firmware_version': self.device_info.firmware_version,
            'sdk_version': self.device_info.sdk_version,
            'last_error': self.last_error,
            'timestamp': datetime.now().isoformat()
        }
    
    def capture_fingerprint(self, thumb_type: str) -> Optional[FingerprintTemplate]:
        """
        Capture a fingerprint from the device.
        
        Args:
            thumb_type (str): 'left' or 'right'
            
        Returns:
            FingerprintTemplate: Captured fingerprint template or None if failed
        """
        if not self.is_initialized:
            raise Exception("Device not initialized")
        
        if not self.device_info.is_connected:
            raise Exception("Device not connected")
        
        if thumb_type not in ['left', 'right']:
            raise ValueError("thumb_type must be 'left' or 'right'")
        
        try:
            logger.info(f"Starting {thumb_type} thumb capture...")
            
            # Mock capture process
            # In real implementation, this would:
            # 1. Wait for finger placement
            # 2. Capture raw fingerprint image
            # 3. Extract minutiae points
            # 4. Generate template
            # 5. Calculate quality score
            
            # Simulate capture delay
            time.sleep(2)
            
            # Generate mock template data
            template = self._generate_mock_template(thumb_type)
            
            logger.info(f"Fingerprint captured successfully: Quality {template.quality_score}%")
            return template
            
        except Exception as e:
            logger.error(f"Fingerprint capture failed: {e}")
            self.last_error = str(e)
            raise
    
    def validate_quality(self, template: FingerprintTemplate) -> Tuple[bool, str]:
        """
        Validate fingerprint template quality.
        
        Args:
            template (FingerprintTemplate): Template to validate
            
        Returns:
            Tuple[bool, str]: (is_valid, message)
        """
        if template.quality_score < self.min_quality_score:
            return False, f"Quality score {template.quality_score}% is below minimum {self.min_quality_score}%"
        
        if template.minutiae_count < self.min_minutiae_count:
            return False, f"Minutiae count {template.minutiae_count} is below minimum {self.min_minutiae_count}"
        
        return True, "Quality validation passed"
    
    def process_template(self, raw_data: str, thumb_type: str) -> FingerprintTemplate:
        """
        Process raw fingerprint data into a template.
        
        Args:
            raw_data (str): Raw fingerprint data
            thumb_type (str): 'left' or 'right'
            
        Returns:
            FingerprintTemplate: Processed template
        """
        try:
            logger.info(f"Processing {thumb_type} thumb template...")
            
            # In real implementation, this would:
            # 1. Decode raw data
            # 2. Apply image enhancement
            # 3. Extract minutiae
            # 4. Generate ISO template
            # 5. Calculate quality metrics
            
            # Mock processing
            time.sleep(1)
            
            template = self._generate_mock_template(thumb_type)
            template.template_data = base64.b64encode(raw_data.encode()).decode()
            
            logger.info("Template processing completed")
            return template
            
        except Exception as e:
            logger.error(f"Template processing failed: {e}")
            raise
    
    def match_templates(self, template1: str, template2: str) -> Tuple[bool, float]:
        """
        Match two fingerprint templates using proper biometric matching.

        Args:
            template1 (str): First template
            template2 (str): Second template

        Returns:
            Tuple[bool, float]: (is_match, confidence_score)
        """
        try:
            # Use the proper fingerprint processor for matching
            from .fingerprint_processing import FingerprintProcessor

            processor = FingerprintProcessor()
            is_match, confidence = processor._compare_templates(template1, template2)

            logger.info(f"🔍 Template matching result: {is_match} (confidence: {confidence:.3f})")

            return is_match, confidence

        except Exception as e:
            logger.error(f"Template matching failed: {e}")
            # Fallback to exact match for safety
            if template1 == template2:
                return True, 1.0
            return False, 0.0
    
    def disconnect(self) -> bool:
        """
        Disconnect from the device.
        
        Returns:
            bool: True if disconnection successful
        """
        try:
            logger.info("Disconnecting from device...")
            
            # In real implementation, this would:
            # 1. Stop any ongoing captures
            # 2. Release device resources
            # 3. Close SDK connection
            
            self.device_info.is_connected = False
            self.is_initialized = False
            
            logger.info("Device disconnected successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to disconnect device: {e}")
            return False
    
    def _generate_mock_template(self, thumb_type: str) -> FingerprintTemplate:
        """Generate mock fingerprint template for development/testing"""
        import random
        
        # Generate mock minutiae points
        minutiae_count = random.randint(35, 60)
        quality_score = random.randint(80, 98)
        
        # Generate mock template data
        template_data = {
            'version': '1.0',
            'thumb_type': thumb_type,
            'minutiae': [
                {
                    'x': random.randint(0, 255),
                    'y': random.randint(0, 255),
                    'angle': random.randint(0, 359),
                    'type': random.choice(['ridge_ending', 'bifurcation'])
                }
                for _ in range(minutiae_count)
            ],
            'quality_metrics': {
                'clarity': random.randint(80, 95),
                'completeness': random.randint(85, 98),
                'uniqueness': random.randint(75, 90)
            }
        }
        
        encoded_template = base64.b64encode(
            json.dumps(template_data).encode()
        ).decode()
        
        return FingerprintTemplate(
            thumb_type=thumb_type,
            template_data=encoded_template,
            quality_score=quality_score,
            minutiae_count=minutiae_count,
            capture_time=datetime.now().isoformat(),
            device_info={
                'model': self.device_info.model,
                'serial_number': self.device_info.serial_number,
                'firmware_version': self.device_info.firmware_version
            }
        )


# Global device instance
_device_instance = None


def get_device() -> FutronicFS88H:
    """Get the global device instance"""
    global _device_instance
    if _device_instance is None:
        # Try real Futronic device first
        try:
            from .futronic_real_device import get_real_device
            real_device = get_real_device()
            if real_device.connected:
                logger.info("✅ Using real Futronic FS88H device")
                _device_instance = RealDeviceWrapper(real_device)
            else:
                logger.info("⚠️ Real device not available, using simulation")
                _device_instance = FutronicFS88H()
        except Exception as e:
            logger.warning(f"Failed to initialize real device: {e}")
            logger.info("Falling back to simulation mode")
            _device_instance = FutronicFS88H()

    return _device_instance


class RealDeviceWrapper(FutronicFS88H):
    """Wrapper to make real device compatible with existing interface"""

    def __init__(self, real_device):
        self.real_device = real_device
        self.device_connected = real_device.connected
        self.device_initialized = real_device.initialized

    def initialize(self) -> bool:
        """Initialize wrapper"""
        return self.real_device.initialize()

    def capture_fingerprint(self, thumb_type: str) -> Optional[FingerprintTemplate]:
        """Capture using real device"""
        return self.real_device.capture_fingerprint(thumb_type)

    def get_device_status(self) -> dict:
        """Get real device status"""
        status = self.real_device.get_device_status()
        return {
            'connected': status['connected'],
            'model': status['model'],
            'interface': status['interface'],
            'real_device': True
        }

    def disconnect(self):
        """Disconnect real device"""
        self.real_device.disconnect()
