#!/usr/bin/env python3
"""
Test the end-to-end duplicate detection system.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from biometrics.fingerprint_processing import FingerprintProcessor
import base64
import json

def test_duplicate_detection():
    """Test the end-to-end duplicate detection system"""
    
    # Real template from database
    template_b64 = "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"
    
    print("🔍 Testing End-to-End Duplicate Detection System")
    print("=" * 60)
    
    processor = FingerprintProcessor()
    
    # Test 1: Check for duplicates with exact same template
    print("\n🔍 TEST 1: Exact Duplicate Detection")
    print("-" * 40)
    
    templates = {
        'left_thumb_fingerprint': template_b64,
        'right_thumb_fingerprint': template_b64
    }

    result = processor.check_duplicates(templates)
    print(f"Duplicate check result: {result}")
    
    # Test 2: Check for duplicates with slightly modified template
    print("\n🔍 TEST 2: Similar Fingerprint Detection")
    print("-" * 40)
    
    # Create slightly modified template
    decoded = json.loads(base64.b64decode(template_b64))
    modified_template = decoded.copy()
    modified_template['minutiae_points'] = decoded['minutiae_points'].copy()
    
    # Modify first 3 points slightly
    for i in range(3):
        point = modified_template['minutiae_points'][i].copy()
        point['x'] += 2  # Small coordinate change
        point['y'] += 1  # Small coordinate change  
        point['angle'] += 5  # Small angle change
        modified_template['minutiae_points'][i] = point
    
    modified_template_b64 = base64.b64encode(json.dumps(modified_template).encode()).decode()
    
    templates_similar = {
        'left_thumb_fingerprint': template_b64,
        'right_thumb_fingerprint': modified_template_b64
    }

    result2 = processor.check_duplicates(templates_similar)
    print(f"Similar fingerprint check result: {result2}")
    
    # Test 3: Check for duplicates with completely different template
    print("\n🔍 TEST 3: Different Fingerprint Detection")
    print("-" * 40)
    
    # Create completely different template
    different_template = decoded.copy()
    different_template['minutiae_points'] = [
        {"x": 100, "y": 100, "angle": 45, "type": "ridge_ending", "quality": 0.9},
        {"x": 120, "y": 150, "angle": 90, "type": "bifurcation", "quality": 0.8},
        {"x": 80, "y": 200, "angle": 135, "type": "ridge_ending", "quality": 0.85},
    ] * 10  # Repeat to get 30 points
    
    different_template_b64 = base64.b64encode(json.dumps(different_template).encode()).decode()
    
    templates_different = {
        'left_thumb': template_b64,
        'right_thumb': different_template_b64
    }
    
    result3 = device_manager.check_for_duplicates(templates_different)
    print(f"Different fingerprint check result: {result3}")
    
    # Summary
    print("\n✅ SUMMARY:")
    exact_duplicate_detected = result.get('has_duplicates', False)
    similar_duplicate_detected = result2.get('has_duplicates', False)
    different_not_detected = not result3.get('has_duplicates', False)
    
    print(f"Exact duplicate detected: {'PASS' if exact_duplicate_detected else 'FAIL'}")
    print(f"Similar fingerprint detected: {'PASS' if similar_duplicate_detected else 'FAIL'}")
    print(f"Different fingerprint not detected: {'PASS' if different_not_detected else 'FAIL'}")
    
    if exact_duplicate_detected and similar_duplicate_detected and different_not_detected:
        print("\n🎉 ALL DUPLICATE DETECTION TESTS PASSED!")
        print("The system correctly identifies duplicate fingerprints and prevents fraud.")
    else:
        print("\n❌ Some duplicate detection tests failed.")
        print("Check the duplicate detection logic.")
    
    return exact_duplicate_detected, similar_duplicate_detected, different_not_detected

if __name__ == "__main__":
    test_duplicate_detection()
