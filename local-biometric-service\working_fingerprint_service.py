#!/usr/bin/env python3
"""
Working Fingerprint Service - Captures real fingerprints using Futronic SDK
"""

import logging
import ctypes
from ctypes import cdll, c_int, c_uint, c_void_p, POINTER, byref
import threading
import queue
import time
import base64
import json
from datetime import datetime
from flask import Flask, jsonify, request
from flask_cors import CORS

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Enable CORS for all routes to allow access from Docker containers
CORS(app, origins=['http://localhost:3000', 'http://127.0.0.1:3000', 'http://0.0.0.0:3000'])

# Constants
FTR_OK = 0
FTR_IMAGE_WIDTH = 320
FTR_IMAGE_HEIGHT = 480
FTR_IMAGE_SIZE = FTR_IMAGE_WIDTH * FTR_IMAGE_HEIGHT

class WorkingFingerprintDevice:
    def __init__(self):
        self.scan_api = None
        self.device_handle = None
        self.connected = False
        self.initialized = False
    
    def initialize(self):
        """Initialize the Futronic device"""
        try:
            logger.info("🔧 Loading Futronic SDK...")
            self.scan_api = cdll.LoadLibrary('./ftrScanAPI.dll')
            
            # Setup function signatures
            self.scan_api.ftrScanOpenDevice.restype = c_void_p
            self.scan_api.ftrScanCloseDevice.argtypes = [c_void_p]
            self.scan_api.ftrScanCloseDevice.restype = c_int
            self.scan_api.ftrScanGetFrame.argtypes = [c_void_p, POINTER(ctypes.c_ubyte), POINTER(c_uint)]
            self.scan_api.ftrScanGetFrame.restype = c_int
            self.scan_api.ftrScanIsFingerPresent.argtypes = [c_void_p, POINTER(c_int)]
            self.scan_api.ftrScanIsFingerPresent.restype = c_int
            
            logger.info("📱 Opening device...")
            self.device_handle = self.scan_api.ftrScanOpenDevice()
            
            if self.device_handle:
                logger.info(f"✅ Device opened! Handle: {self.device_handle}")
                self.connected = True
                self.initialized = True
                return True
            else:
                logger.error("❌ Failed to open device")
                return False
                
        except Exception as e:
            logger.error(f"❌ Initialization error: {e}")
            return False
    
    def wait_for_finger(self, timeout_seconds=30):
        """Wait for finger to be placed on scanner"""
        logger.info("👆 Please place your finger on the scanner...")

        start_time = time.time()
        check_count = 0
        while (time.time() - start_time) < timeout_seconds:
            try:
                finger_present = c_int()
                result = self.scan_api.ftrScanIsFingerPresent(self.device_handle, byref(finger_present))
                check_count += 1

                # Log progress every 50 checks (about 5 seconds)
                if check_count % 50 == 0:
                    elapsed = time.time() - start_time
                    logger.info(f"⏱️ Waiting for finger... ({elapsed:.1f}s elapsed)")

                if result == FTR_OK and finger_present.value > 0:
                    logger.info("🔍 Finger detected, confirming...")
                    # Double-check finger presence
                    time.sleep(0.3)
                    finger_present2 = c_int()
                    result2 = self.scan_api.ftrScanIsFingerPresent(self.device_handle, byref(finger_present2))

                    if result2 == FTR_OK and finger_present2.value > 0:
                        logger.info("✅ Finger detected and confirmed!")
                        return True
                    else:
                        logger.info("⚠️ Finger detection not confirmed, continuing to wait...")

                time.sleep(0.1)

            except Exception as e:
                logger.error(f"❌ Finger detection error: {e}")
                logger.error(f"   Device handle: {self.device_handle}")
                logger.error(f"   Connected: {self.connected}")
                time.sleep(0.5)

                # Try to recover from errors
                if check_count > 10:  # After some attempts
                    logger.warning("🔄 Attempting to recover from detection errors...")
                    return False

        logger.warning(f"⚠️ Finger detection timeout after {timeout_seconds} seconds")
        return False
    
    def capture_frame_threaded(self, result_queue, timeout_seconds=10):
        """Capture frame in separate thread"""
        try:
            image_buffer = (ctypes.c_ubyte * FTR_IMAGE_SIZE)()
            frame_size = c_uint(FTR_IMAGE_SIZE)
            
            logger.info("📸 Capturing fingerprint image...")
            start_time = time.time()
            
            result = self.scan_api.ftrScanGetFrame(self.device_handle, image_buffer, byref(frame_size))
            
            end_time = time.time()
            
            result_queue.put({
                'success': True,
                'result_code': result,
                'frame_size': frame_size.value,
                'capture_time': end_time - start_time,
                'image_buffer': bytes(image_buffer[:frame_size.value]) if result == FTR_OK else None
            })
            
        except Exception as e:
            result_queue.put({
                'success': False,
                'error': str(e)
            })
    
    def capture_fingerprint(self, thumb_type='left'):
        """Capture a real fingerprint"""
        try:
            if not self.connected:
                logger.error("❌ Device not connected")
                return {
                    'success': False,
                    'error': 'Device not connected'
                }

            if not self.device_handle:
                logger.error("❌ No device handle available")
                return {
                    'success': False,
                    'error': 'Device handle not available'
                }

            logger.info(f"🔍 Starting {thumb_type} thumb capture...")
            logger.info(f"📱 Device handle: {self.device_handle}")
            logger.info(f"🔗 Device connected: {self.connected}")

            # Step 1: Wait for finger
            logger.info("📍 Step 1: Waiting for finger placement...")
            if not self.wait_for_finger(30):
                logger.warning("⚠️ Finger placement timeout")
                return {
                    'success': False,
                    'error': 'Timeout waiting for finger placement. Please ensure your finger is properly placed on the scanner and try again.'
                }
            
            # Step 2: Capture frame with threading
            logger.info("📍 Step 2: Capturing fingerprint frame...")
            result_queue = queue.Queue()
            capture_thread = threading.Thread(
                target=self.capture_frame_threaded,
                args=(result_queue, 15)  # Increased timeout
            )
            capture_thread.daemon = True
            capture_thread.start()

            # Wait for capture result
            try:
                logger.info("⏱️ Waiting for capture result...")
                capture_result = result_queue.get(timeout=15)  # Increased timeout

                if not capture_result['success']:
                    error_msg = capture_result.get('error', 'Unknown capture error')
                    logger.error(f"❌ Capture failed: {error_msg}")
                    return {
                        'success': False,
                        'error': f"Capture failed: {error_msg}"
                    }
                
                if capture_result['result_code'] != FTR_OK:
                    return {
                        'success': False,
                        'error': f"Frame capture failed with code: {capture_result['result_code']}"
                    }
                
                image_data = capture_result['image_buffer']
                frame_size = capture_result['frame_size']
                
                if not image_data or frame_size < 1000:
                    return {
                        'success': False,
                        'error': f"Invalid frame data: size={frame_size}"
                    }
                
                # Analyze image quality
                quality_info = self.analyze_image_quality(image_data, frame_size)
                
                if quality_info['quality_score'] < 30:
                    return {
                        'success': False,
                        'error': f"Poor image quality: {quality_info['quality_score']}% - please try again"
                    }
                
                # Create template data
                template_data = {
                    'version': '1.0',
                    'thumb_type': thumb_type,
                    'image_width': FTR_IMAGE_WIDTH,
                    'image_height': FTR_IMAGE_HEIGHT,
                    'frame_size': frame_size,
                    'quality_score': quality_info['quality_score'],
                    'capture_time': datetime.now().isoformat(),
                    'device_info': {
                        'model': 'Futronic FS88H',
                        'interface': 'working_sdk',
                        'real_device': True
                    },
                    'image_hash': hash(image_data) & 0x7FFFFFFF
                }
                
                # Encode template
                template_encoded = base64.b64encode(json.dumps(template_data).encode()).decode()
                
                logger.info(f"✅ Fingerprint captured successfully!")
                logger.info(f"📊 Quality: {quality_info['quality_score']}%")
                logger.info(f"📊 Frame size: {frame_size} bytes")
                
                return {
                    'success': True,
                    'data': {
                        'thumb_type': thumb_type,
                        'template_data': template_encoded,
                        'quality_score': quality_info['quality_score'],
                        'minutiae_count': quality_info['estimated_minutiae'],
                        'capture_time': template_data['capture_time'],
                        'device_info': template_data['device_info'],
                        'frame_size': frame_size
                    }
                }
                
            except queue.Empty:
                logger.error("❌ Capture timeout - no result received")
                return {
                    'success': False,
                    'error': 'Capture timeout - please ensure your finger remains on the scanner and try again'
                }

        except Exception as e:
            logger.error(f"❌ Unexpected capture error: {e}")
            logger.error(f"   Error type: {type(e).__name__}")
            import traceback
            logger.error(f"   Traceback: {traceback.format_exc()}")

            # Try to maintain device connection
            if self.device_handle and self.scan_api:
                try:
                    # Test if device is still responsive
                    finger_present = c_int()
                    test_result = self.scan_api.ftrScanIsFingerPresent(self.device_handle, byref(finger_present))
                    if test_result != FTR_OK:
                        logger.warning("⚠️ Device may have disconnected, attempting to maintain connection...")
                        self.connected = False
                except:
                    logger.warning("⚠️ Device connection test failed")
                    self.connected = False

            return {
                'success': False,
                'error': f'Capture error: {str(e)}. Please try again or restart the service if the problem persists.'
            }
    
    def analyze_image_quality(self, image_data, frame_size):
        """Analyze captured image quality"""
        try:
            if not image_data or frame_size < 100:
                return {'quality_score': 0, 'estimated_minutiae': 0}
            
            # Sample pixels for analysis
            sample_size = min(2000, len(image_data))
            pixels = list(image_data[:sample_size])
            
            # Calculate statistics
            non_zero_pixels = sum(1 for p in pixels if p > 10)
            data_percentage = (non_zero_pixels / sample_size) * 100
            
            avg_pixel = sum(pixels) / len(pixels)
            pixel_variance = sum((p - avg_pixel) ** 2 for p in pixels) / len(pixels)
            
            # Calculate quality score
            quality_score = min(100, max(0, 
                (data_percentage * 0.6) + 
                (min(pixel_variance / 10, 40)) +
                (min(avg_pixel / 2, 20))
            ))
            
            # Estimate minutiae count based on quality
            estimated_minutiae = max(20, min(60, int(quality_score * 0.8)))
            
            return {
                'quality_score': int(quality_score),
                'estimated_minutiae': estimated_minutiae,
                'data_percentage': round(data_percentage, 1),
                'avg_pixel': round(avg_pixel, 1),
                'variance': round(pixel_variance, 1)
            }
            
        except Exception as e:
            logger.error(f"Quality analysis error: {e}")
            return {'quality_score': 50, 'estimated_minutiae': 30}
    
    def get_status(self):
        """Get device status"""
        return {
            'connected': self.connected,
            'initialized': self.initialized,
            'handle': self.device_handle,
            'model': 'Futronic FS88H',
            'interface': 'working_sdk'
        }
    
    def close(self):
        """Close device"""
        if self.device_handle and self.scan_api:
            try:
                self.scan_api.ftrScanCloseDevice(self.device_handle)
                logger.info("🔒 Device closed")
            except Exception as e:
                logger.error(f"Close error: {e}")

# Global device
device = WorkingFingerprintDevice()

@app.route('/api/device/status', methods=['GET'])
def get_device_status():
    """Get device status"""
    try:
        status = device.get_status()
        return jsonify({
            'success': True,
            'data': status
        })
    except Exception as e:
        logger.error(f"❌ Device status error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/device/reconnect', methods=['POST'])
def reconnect_device():
    """Reconnect to device"""
    try:
        logger.info("🔄 Attempting device reconnection...")

        # Close existing connection
        device.close()

        # Reinitialize
        if device.initialize():
            logger.info("✅ Device reconnected successfully")
            return jsonify({
                'success': True,
                'message': 'Device reconnected successfully',
                'data': device.get_status()
            })
        else:
            logger.error("❌ Device reconnection failed")
            return jsonify({
                'success': False,
                'error': 'Device reconnection failed'
            }), 503

    except Exception as e:
        logger.error(f"❌ Reconnection error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/capture/fingerprint', methods=['GET', 'POST'])
def capture_fingerprint():
    """Capture real fingerprint"""
    try:
        if request.method == 'GET':
            thumb_type = request.args.get('thumb_type', 'left')
        else:
            data = request.get_json() or {}
            thumb_type = data.get('thumb_type', 'left')

        if thumb_type not in ['left', 'right']:
            return jsonify({
                'success': False,
                'error': 'Invalid thumb_type. Must be "left" or "right"'
            }), 400

        logger.info(f"🌐 API: Received {thumb_type} thumb capture request")

        # Check device status before capture
        if not device.connected:
            logger.warning("⚠️ Device not connected, attempting to reconnect...")
            if not device.initialize():
                return jsonify({
                    'success': False,
                    'error': 'Device not connected and reconnection failed'
                }), 503

        result = device.capture_fingerprint(thumb_type)

        if result['success']:
            logger.info(f"✅ API: {thumb_type} thumb capture successful")
            return jsonify(result)
        else:
            logger.warning(f"⚠️ API: {thumb_type} thumb capture failed: {result.get('error', 'Unknown error')}")
            return jsonify(result), 500

    except Exception as e:
        logger.error(f"❌ API: Capture endpoint error: {e}")
        return jsonify({
            'success': False,
            'error': f'Service error: {str(e)}'
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check"""
    return jsonify({
        'status': 'healthy',
        'service': 'Working Fingerprint Service',
        'timestamp': datetime.now().isoformat(),
        'device_connected': device.connected
    })

@app.route('/', methods=['GET'])
def index():
    """Web interface"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Working Fingerprint Service</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            button { padding: 15px 30px; margin: 10px; font-size: 16px; }
            .result { margin: 20px 0; padding: 15px; border-radius: 5px; }
            .success { background: #d4edda; color: #155724; }
            .error { background: #f8d7da; color: #721c24; }
        </style>
    </head>
    <body>
        <h1>🔒 Working Fingerprint Service</h1>
        <p>This service captures real fingerprints from your Futronic FS88H device.</p>
        
        <button onclick="captureFingerprint('left')">👈 Capture Left Thumb</button>
        <button onclick="captureFingerprint('right')">👉 Capture Right Thumb</button>
        
        <div id="result"></div>
        
        <script>
            async function captureFingerprint(thumbType) {
                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = '<div class="result">📸 Starting capture... Please place your finger on the scanner when prompted.</div>';
                
                try {
                    const response = await fetch(`/api/capture/fingerprint?thumb_type=${thumbType}`);
                    const data = await response.json();
                    
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="result success">
                                <h3>✅ Fingerprint Captured Successfully!</h3>
                                <p><strong>Thumb:</strong> ${data.data.thumb_type}</p>
                                <p><strong>Quality:</strong> ${data.data.quality_score}%</p>
                                <p><strong>Minutiae:</strong> ${data.data.minutiae_count}</p>
                                <p><strong>Frame Size:</strong> ${data.data.frame_size} bytes</p>
                                <p><strong>Real Device:</strong> ${data.data.device_info.real_device ? 'Yes' : 'No'}</p>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="result error">
                                <h3>❌ Capture Failed</h3>
                                <p>${data.error}</p>
                            </div>
                        `;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ Network Error</h3>
                            <p>${error.message}</p>
                        </div>
                    `;
                }
            }
        </script>
    </body>
    </html>
    """

if __name__ == '__main__':
    try:
        logger.info("🚀 Starting Working Fingerprint Service")
        logger.info("=" * 50)

        # Initialize device
        logger.info("📱 Initializing Futronic FS88H device...")
        if device.initialize():
            logger.info("✅ Device ready for fingerprint capture")
            logger.info(f"📊 Device handle: {device.device_handle}")
            logger.info(f"🔗 Device connected: {device.connected}")
        else:
            logger.error("❌ Device initialization failed")
            logger.error("   Please check:")
            logger.error("   - Futronic FS88H device is connected via USB")
            logger.error("   - Device drivers are installed")
            logger.error("   - DLL files are available")
            logger.error("   - No other software is using the device")
            exit(1)

        # Start service
        logger.info("=" * 50)
        logger.info("🌐 Service running at http://localhost:8001")
        logger.info("🌐 Service also available at http://0.0.0.0:8001")
        logger.info("🔍 Health check: http://localhost:8001/api/health")
        logger.info("📱 Device status: http://localhost:8001/api/device/status")
        logger.info("👆 Ready to capture real fingerprints!")
        logger.info("=" * 50)

        # Run with better error handling
        app.run(host='0.0.0.0', port=8001, debug=False, threaded=True, use_reloader=False)

    except KeyboardInterrupt:
        logger.info("🛑 Service stopped by user")
    except OSError as e:
        if "Address already in use" in str(e):
            logger.error("❌ Port 8001 is already in use")
            logger.error("   Please stop any other service using port 8001 or change the port")
        else:
            logger.error(f"❌ Network error: {e}")
    except Exception as e:
        logger.error(f"❌ Unexpected service error: {e}")
        import traceback
        logger.error(f"   Traceback: {traceback.format_exc()}")
    finally:
        logger.info("🔒 Closing device connection...")
        try:
            device.close()
            logger.info("✅ Device closed successfully")
        except Exception as e:
            logger.error(f"❌ Error closing device: {e}")
        logger.info("👋 Service shutdown complete")
