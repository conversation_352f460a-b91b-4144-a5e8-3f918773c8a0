"""
Fingerprint processing utilities for quality validation, 
duplicate detection, and template management.
"""

import json
import base64
import logging
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
from django.db import connection
from django_tenants.utils import schema_context, get_tenant_model

logger = logging.getLogger(__name__)


class FingerprintProcessor:
    """
    Handles fingerprint template processing, validation, and duplicate detection.
    """
    
    def __init__(self):
        self.quality_threshold = 75
        self.match_threshold = 0.3  # Very low threshold for testing duplicate detection
        self.min_minutiae = 30
        self.debug_mode = False  # Disabled - using real duplicate detection
    
    def validate_template_quality(self, template_data: str) -> Dict:
        """
        Validate the quality of a fingerprint template.
        
        Args:
            template_data (str): Base64 encoded fingerprint template
            
        Returns:
            Dict: Quality validation result
        """
        try:
            # Decode and parse template
            decoded_data = base64.b64decode(template_data)
            template = json.loads(decoded_data.decode())
            
            quality_score = template.get('quality_metrics', {}).get('clarity', 0)
            minutiae_count = len(template.get('minutiae', []))
            
            # Validate quality criteria
            is_valid = True
            issues = []
            
            if quality_score < self.quality_threshold:
                is_valid = False
                issues.append(f"Quality score {quality_score}% below threshold {self.quality_threshold}%")
            
            if minutiae_count < self.min_minutiae:
                is_valid = False
                issues.append(f"Minutiae count {minutiae_count} below minimum {self.min_minutiae}")
            
            return {
                'is_valid': is_valid,
                'quality_score': quality_score,
                'minutiae_count': minutiae_count,
                'issues': issues,
                'validation_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error validating template quality: {e}")
            return {
                'is_valid': False,
                'quality_score': 0,
                'minutiae_count': 0,
                'issues': [f"Template validation error: {str(e)}"],
                'validation_time': datetime.now().isoformat()
            }
    
    def check_duplicates(self, fingerprint_data: Dict) -> Dict:
        """
        Check for duplicate fingerprints across all tenants.

        Args:
            fingerprint_data (Dict): Dictionary containing fingerprint templates

        Returns:
            Dict: Duplicate check results
        """
        try:
            logger.info("🔍 DUPLICATE CHECK STARTED")
            logger.info(f"🔍 Input data keys: {list(fingerprint_data.keys())}")

            matches = []
            left_thumb = fingerprint_data.get('left_thumb_fingerprint')
            right_thumb = fingerprint_data.get('right_thumb_fingerprint')

            logger.info(f"🔍 Left thumb data: {len(left_thumb) if left_thumb else 0} chars")
            logger.info(f"🔍 Right thumb data: {len(right_thumb) if right_thumb else 0} chars")

            if not left_thumb and not right_thumb:
                logger.info("❌ No fingerprint data to check")
                return {
                    'has_duplicates': False,
                    'matches': [],
                    'check_time': datetime.now().isoformat()
                }

            # TEMPORARY TEST MODE - Force duplicate detection to verify the system is working
            if self.debug_mode and (left_thumb or right_thumb):
                logger.info("🧪 DEBUG MODE: Forcing duplicate detection for testing")
                test_match = {
                    'tenant_id': 999,
                    'tenant_name': 'Test Kebele (Debug Mode)',
                    'citizen_id': 999,
                    'citizen_name': 'Debug Test Citizen',
                    'citizen_digital_id': 'DEBUG-TEST-123',
                    'match_score': 0.95,
                    'match_details': ['Debug mode forced duplicate'],
                    'biometric_id': 999
                }
                logger.info("🧪 DEBUG MODE: Returning forced duplicate match")
                return {
                    'has_duplicates': True,
                    'matches': [test_match],
                    'total_matches': 1,
                    'check_time': datetime.now().isoformat(),
                    'debug_mode': True
                }

            # Get all kebele tenants
            Tenant = get_tenant_model()
            tenants = Tenant.objects.filter(type='kebele')
            logger.info(f"🔍 Checking {tenants.count()} kebele tenants for duplicates")

            for tenant in tenants:
                try:
                    logger.info(f"🔍 Checking tenant: {tenant.name} (schema: {tenant.schema_name})")
                    with schema_context(tenant.schema_name):
                        tenant_matches = self._check_tenant_duplicates(
                            left_thumb, right_thumb, tenant
                        )
                        logger.info(f"🔍 Found {len(tenant_matches)} matches in {tenant.name}")
                        matches.extend(tenant_matches)
                except Exception as e:
                    logger.error(f"Error checking duplicates in tenant {tenant.name}: {e}")
                    continue

            logger.info(f"🔍 DUPLICATE CHECK COMPLETED: {len(matches)} total matches found")

            return {
                'has_duplicates': len(matches) > 0,
                'matches': matches,
                'total_matches': len(matches),
                'check_time': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error checking duplicates: {e}")
            return {
                'has_duplicates': False,
                'matches': [],
                'error': str(e),
                'check_time': datetime.now().isoformat()
            }
    
    def _check_tenant_duplicates(self, left_thumb: str, right_thumb: str, tenant) -> List[Dict]:
        """
        Check for duplicates within a specific tenant.

        Args:
            left_thumb (str): Left thumb template
            right_thumb (str): Right thumb template
            tenant: Tenant object

        Returns:
            List[Dict]: List of matches found
        """
        matches = []

        try:
            # Import here to avoid circular imports
            from tenants.models.citizen import Biometric

            # Get all biometric records in this tenant
            biometrics = Biometric.objects.select_related('citizen').all()
            logger.info(f"🔍 Tenant {tenant.name}: Found {biometrics.count()} biometric records to check")

            for i, biometric in enumerate(biometrics):
                logger.info(f"🔍 Checking biometric {i+1}/{biometrics.count()}: Citizen {biometric.citizen.first_name} {biometric.citizen.last_name}")

                match_score = 0.0
                match_details = []

                # Check left thumb match
                if left_thumb and biometric.left_thumb_fingerprint:
                    logger.info(f"🔍 Comparing left thumbs...")
                    logger.info(f"   Input left thumb: {len(left_thumb)} chars")
                    logger.info(f"   Stored left thumb: {len(biometric.left_thumb_fingerprint)} chars")

                    left_match, left_confidence = self._compare_templates(
                        left_thumb, biometric.left_thumb_fingerprint
                    )
                    logger.info(f"   Left thumb result: match={left_match}, confidence={left_confidence:.3f}")

                    if left_match:
                        match_score += 0.5
                        match_details.append(f"Left thumb match (confidence: {left_confidence:.2f})")
                        logger.info(f"✅ LEFT THUMB MATCH DETECTED!")

                # Check right thumb match
                if right_thumb and biometric.right_thumb_fingerprint:
                    logger.info(f"🔍 Comparing right thumbs...")
                    logger.info(f"   Input right thumb: {len(right_thumb)} chars")
                    logger.info(f"   Stored right thumb: {len(biometric.right_thumb_fingerprint)} chars")

                    right_match, right_confidence = self._compare_templates(
                        right_thumb, biometric.right_thumb_fingerprint
                    )
                    logger.info(f"   Right thumb result: match={right_match}, confidence={right_confidence:.3f}")

                    if right_match:
                        match_score += 0.5
                        match_details.append(f"Right thumb match (confidence: {right_confidence:.2f})")
                        logger.info(f"✅ RIGHT THUMB MATCH DETECTED!")

                # If we have a significant match, record it
                if match_score >= 0.5:  # At least one thumb matches
                    logger.info(f"🚨 DUPLICATE FOUND: {biometric.citizen.first_name} {biometric.citizen.last_name} (score: {match_score})")
                    matches.append({
                        'tenant_id': tenant.id,
                        'tenant_name': tenant.name,
                        'citizen_id': biometric.citizen.id,
                        'citizen_name': f"{biometric.citizen.first_name} {biometric.citizen.last_name}",
                        'citizen_digital_id': biometric.citizen.digital_id,
                        'match_score': match_score,
                        'match_details': match_details,
                        'biometric_id': biometric.id
                    })
                else:
                    logger.info(f"❌ No match for {biometric.citizen.first_name} {biometric.citizen.last_name} (score: {match_score})")

            logger.info(f"🔍 Tenant {tenant.name} check complete: {len(matches)} matches found")

        except Exception as e:
            logger.error(f"Error checking tenant {tenant.name} for duplicates: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")

        return matches
    
    def _compare_templates(self, template1: str, template2: str) -> Tuple[bool, float]:
        """
        Compare two fingerprint templates.

        Args:
            template1 (str): First template
            template2 (str): Second template

        Returns:
            Tuple[bool, float]: (is_match, confidence_score)
        """
        try:
            logger.info(f"🔍 Comparing fingerprint templates...")
            logger.info(f"   Template 1 length: {len(template1) if template1 else 0}")
            logger.info(f"   Template 2 length: {len(template2) if template2 else 0}")

            # Check for exact match first
            if template1 == template2:
                logger.info("✅ Exact template match found!")
                return True, 1.0

            # For real biometric data, we need to compare the actual fingerprint characteristics
            # Since we're using real Futronic device data, let's implement a more realistic comparison

            try:
                # Try to decode and compare templates
                decoded1 = json.loads(base64.b64decode(template1).decode())
                decoded2 = json.loads(base64.b64decode(template2).decode())

                logger.info(f"🔍 Decoded templates successfully")
                logger.info(f"   Template 1 version: {decoded1.get('version', 'Unknown')}")
                logger.info(f"   Template 2 version: {decoded2.get('version', 'Unknown')}")
                logger.info(f"   Template 1 device: {decoded1.get('device_info', {}).get('model', 'Unknown')}")
                logger.info(f"   Template 2 device: {decoded2.get('device_info', {}).get('model', 'Unknown')}")

                # Check if we have actual fingerprint image data (version 2.0+)
                has_image1 = 'fingerprint_image' in decoded1 and decoded1['fingerprint_image']
                has_image2 = 'fingerprint_image' in decoded2 and decoded2['fingerprint_image']

                logger.info(f"   Template 1 has real fingerprint: {has_image1}")
                logger.info(f"   Template 2 has real fingerprint: {has_image2}")

                similarity_score = 0.0

                # If both templates have actual fingerprint images, compare them
                if has_image1 and has_image2:
                    logger.info("🔍 Comparing actual fingerprint images...")

                    # Get the actual fingerprint image data
                    image1_b64 = decoded1['fingerprint_image']
                    image2_b64 = decoded2['fingerprint_image']

                    # Compare image data directly
                    if image1_b64 == image2_b64:
                        # Identical fingerprint images - definite match
                        similarity_score = 1.0
                        logger.info("🚨 IDENTICAL FINGERPRINT IMAGES DETECTED!")
                    else:
                        # Different images - compare image characteristics
                        try:
                            image1_data = base64.b64decode(image1_b64)
                            image2_data = base64.b64decode(image2_b64)

                            # Compare image sizes
                            size_diff = abs(len(image1_data) - len(image2_data))
                            max_size = max(len(image1_data), len(image2_data))
                            size_similarity = 1.0 - (size_diff / max_size) if max_size > 0 else 0.0

                            # Compare first 1000 bytes for pattern similarity
                            sample_size = min(1000, len(image1_data), len(image2_data))
                            if sample_size > 0:
                                sample1 = image1_data[:sample_size]
                                sample2 = image2_data[:sample_size]

                                # Calculate byte-level similarity
                                matching_bytes = sum(1 for a, b in zip(sample1, sample2) if a == b)
                                byte_similarity = matching_bytes / sample_size

                                # Combine similarities
                                image_similarity = (size_similarity * 0.3) + (byte_similarity * 0.7)
                                similarity_score += image_similarity * 0.8  # High weight for image comparison

                                logger.info(f"🔍 Image comparison:")
                                logger.info(f"   Size similarity: {size_similarity:.3f}")
                                logger.info(f"   Byte similarity: {byte_similarity:.3f}")
                                logger.info(f"   Combined image similarity: {image_similarity:.3f}")

                        except Exception as img_error:
                            logger.warning(f"Error comparing fingerprint images: {img_error}")
                            similarity_score += 0.1  # Small penalty for comparison error

                # Additional metadata comparison for context
                # 1. Check if both are from the same device type
                device1 = decoded1.get('device_info', {}).get('model', '')
                device2 = decoded2.get('device_info', {}).get('model', '')
                if device1 == device2 and device1 == 'Futronic FS88H':
                    similarity_score += 0.1  # Lower weight since we have image data
                    logger.info("✅ Same device type detected")

                # 2. Compare thumb types
                thumb1 = decoded1.get('thumb_type', '')
                thumb2 = decoded2.get('thumb_type', '')
                if thumb1 == thumb2:
                    similarity_score += 0.1
                    logger.info(f"✅ Same thumb type: {thumb1}")

                # Determine if it's a match
                is_match = similarity_score >= self.match_threshold

                logger.info(f"🔍 Real fingerprint comparison result:")
                logger.info(f"   Similarity score: {similarity_score:.3f}")
                logger.info(f"   Match threshold: {self.match_threshold}")
                logger.info(f"   Is match: {is_match}")
                logger.info(f"   Comparison type: {'Real fingerprint images' if has_image1 and has_image2 else 'Metadata only'}")

                return is_match, similarity_score

            except Exception as decode_error:
                logger.warning(f"Could not decode templates as JSON: {decode_error}")

                # Fallback: Use string similarity for basic comparison
                # This handles cases where templates might be similar but not identical
                if len(template1) > 100 and len(template2) > 100:
                    # For longer templates, check if they share significant portions
                    common_chars = sum(1 for a, b in zip(template1[:100], template2[:100]) if a == b)
                    similarity = common_chars / 100.0

                    # Also check overall length similarity
                    length_similarity = min(len(template1), len(template2)) / max(len(template1), len(template2))

                    # Combine similarities
                    overall_similarity = (similarity * 0.7) + (length_similarity * 0.3)

                    is_match = overall_similarity >= self.match_threshold

                    logger.info(f"🔍 Fallback string comparison:")
                    logger.info(f"   Character similarity: {similarity:.2f}")
                    logger.info(f"   Length similarity: {length_similarity:.2f}")
                    logger.info(f"   Overall similarity: {overall_similarity:.2f}")
                    logger.info(f"   Is match: {is_match}")

                    return is_match, overall_similarity
                else:
                    # For very short templates or other cases, use a simple hash-based comparison
                    # This is a fallback for testing - if templates are identical, they should match
                    if template1 and template2:
                        # Check if templates are very similar (for testing purposes)
                        if len(template1) == len(template2):
                            # If same length, check character similarity
                            similarity = sum(1 for a, b in zip(template1, template2) if a == b) / len(template1)
                            is_match = similarity >= 0.9  # 90% character similarity
                            logger.info(f"🔍 Short template comparison: {similarity:.2f} similarity")
                            return is_match, similarity

                    return False, 0.0

        except Exception as e:
            logger.error(f"Error comparing templates: {e}")
            return False, 0.0
    
    def _calculate_minutiae_similarity(self, minutiae1: List, minutiae2: List) -> float:
        """
        Calculate similarity between two sets of minutiae points.
        
        This is a simplified implementation for development.
        Production systems would use proper biometric matching algorithms.
        """
        if not minutiae1 or not minutiae2:
            return 0.0
        
        # Simple similarity based on minutiae count
        count_similarity = min(len(minutiae1), len(minutiae2)) / max(len(minutiae1), len(minutiae2))
        
        # Add some randomness for demo purposes
        import random
        position_similarity = random.uniform(0.6, 0.9)
        
        # Combine similarities
        overall_similarity = (count_similarity * 0.3) + (position_similarity * 0.7)
        
        return min(overall_similarity, 1.0)
    
    def get_biometric_statistics(self) -> Dict:
        """
        Get biometric statistics for the current tenant.

        Returns:
            Dict: Statistics about biometric data
        """
        try:
            # Import here to avoid circular imports
            from tenants.models.citizen import Biometric, Citizen

            # Count total citizens and biometric records
            total_citizens = Citizen.objects.count()
            total_biometrics = Biometric.objects.count()
            
            # Count by fingerprint availability
            try:
                left_thumb_count = Biometric.objects.exclude(
                    left_thumb_fingerprint__isnull=True
                ).exclude(left_thumb_fingerprint='').count()

                right_thumb_count = Biometric.objects.exclude(
                    right_thumb_fingerprint__isnull=True
                ).exclude(right_thumb_fingerprint='').count()

                both_thumbs_count = Biometric.objects.exclude(
                    left_thumb_fingerprint__isnull=True
                ).exclude(left_thumb_fingerprint='').exclude(
                    right_thumb_fingerprint__isnull=True
                ).exclude(right_thumb_fingerprint='').count()
            except Exception as query_error:
                logger.warning(f"Error querying biometric data: {query_error}")
                # Fallback to zero counts
                left_thumb_count = 0
                right_thumb_count = 0
                both_thumbs_count = 0
            
            # Calculate percentages
            biometric_coverage = (total_biometrics / total_citizens * 100) if total_citizens > 0 else 0
            left_thumb_coverage = (left_thumb_count / total_citizens * 100) if total_citizens > 0 else 0
            right_thumb_coverage = (right_thumb_count / total_citizens * 100) if total_citizens > 0 else 0
            complete_coverage = (both_thumbs_count / total_citizens * 100) if total_citizens > 0 else 0
            
            return {
                'total_citizens': total_citizens,
                'total_biometric_records': total_biometrics,
                'biometric_coverage_percent': round(biometric_coverage, 2),
                'fingerprint_stats': {
                    'left_thumb_count': left_thumb_count,
                    'right_thumb_count': right_thumb_count,
                    'both_thumbs_count': both_thumbs_count,
                    'left_thumb_coverage_percent': round(left_thumb_coverage, 2),
                    'right_thumb_coverage_percent': round(right_thumb_coverage, 2),
                    'complete_coverage_percent': round(complete_coverage, 2)
                },
                'generated_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting biometric statistics: {e}")
            return {
                'error': str(e),
                'generated_at': datetime.now().isoformat()
            }
