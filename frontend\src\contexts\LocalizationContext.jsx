/**
 * Localization Context for GoID System
 * Provides translation and language switching functionality
 */

import React, { createContext, useContext, useState, useEffect } from 'react';
import localizationService from '../services/localizationService';

const LocalizationContext = createContext();

export const useLocalization = () => {
  const context = useContext(LocalizationContext);
  if (!context) {
    throw new Error('useLocalization must be used within a LocalizationProvider');
  }
  return context;
};

export const LocalizationProvider = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState(localizationService.getCurrentLanguage());
  const [isLoading, setIsLoading] = useState(true);
  const [isChangingLanguage, setIsChangingLanguage] = useState(false);

  // Initialize localization on mount
  useEffect(() => {
    const initializeLocalization = async () => {
      setIsLoading(true);
      try {
        await localizationService.initialize();
        setCurrentLanguage(localizationService.getCurrentLanguage());
      } catch (error) {
        console.error('Failed to initialize localization:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeLocalization();
  }, []);

  // Update document direction when language changes
  useEffect(() => {
    document.documentElement.dir = localizationService.getDirection();
    document.documentElement.lang = currentLanguage;
  }, [currentLanguage]);

  /**
   * Change language
   */
  const changeLanguage = async (languageCode) => {
    if (languageCode === currentLanguage) {
      return true;
    }

    setIsChangingLanguage(true);
    try {
      const success = await localizationService.setLanguage(languageCode);
      if (success) {
        setCurrentLanguage(languageCode);
        
        // Show success message
        if (window.showNotification) {
          window.showNotification(
            localizationService.t('language_changed', 'Language changed successfully'),
            'success'
          );
        }
        
        return true;
      } else {
        // Still update local state even if backend fails
        setCurrentLanguage(languageCode);
        return false;
      }
    } catch (error) {
      console.error('Failed to change language:', error);
      return false;
    } finally {
      setIsChangingLanguage(false);
    }
  };

  /**
   * Get translation for a key
   */
  const t = (key, defaultValue = null) => {
    return localizationService.t(key, defaultValue);
  };

  /**
   * Get translation with parameters
   */
  const tp = (key, params = {}, defaultValue = null) => {
    return localizationService.tp(key, params, defaultValue);
  };

  /**
   * Get available languages
   */
  const getAvailableLanguages = () => {
    return localizationService.getAvailableLanguages();
  };

  /**
   * Check if current language is RTL
   */
  const isRTL = () => {
    return localizationService.isRTL();
  };

  /**
   * Get language direction
   */
  const getDirection = () => {
    return localizationService.getDirection();
  };

  /**
   * Get current language info
   */
  const getCurrentLanguageInfo = () => {
    const languages = getAvailableLanguages();
    return languages.find(lang => lang.code === currentLanguage) || languages[0];
  };

  const value = {
    // State
    currentLanguage,
    isLoading,
    isChangingLanguage,
    
    // Functions
    changeLanguage,
    t,
    tp,
    getAvailableLanguages,
    isRTL,
    getDirection,
    getCurrentLanguageInfo,
    
    // Convenience properties
    isAmharic: currentLanguage === 'am',
    isEnglish: currentLanguage === 'en',
    direction: getDirection(),
    languageInfo: getCurrentLanguageInfo()
  };

  return (
    <LocalizationContext.Provider value={value}>
      {children}
    </LocalizationContext.Provider>
  );
};

export default LocalizationContext;
