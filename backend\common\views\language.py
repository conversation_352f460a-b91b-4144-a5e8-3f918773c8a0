"""
Language switching views for GoID system
"""

from django.http import JsonResponse
from django.utils import translation
from django.utils.translation import gettext as _
from django.conf import settings
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
import json


@api_view(['GET'])
def get_available_languages(request):
    """Get list of available languages"""
    
    languages = []
    for code, name in settings.LANGUAGES:
        languages.append({
            'code': code,
            'name': name,
            'is_current': code == translation.get_language()
        })
    
    return Response({
        'languages': languages,
        'current_language': translation.get_language()
    })


@api_view(['POST'])
def set_language(request):
    """Set user's preferred language"""
    
    try:
        data = json.loads(request.body) if request.body else {}
        language_code = data.get('language', 'en')
        
        # Validate language code
        available_languages = [code for code, name in settings.LANGUAGES]
        if language_code not in available_languages:
            return Response({
                'error': _('Invalid language code'),
                'available_languages': available_languages
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Activate the language
        translation.activate(language_code)
        
        # Set language in session if available
        if hasattr(request, 'session'):
            request.session[translation.LANGUAGE_SESSION_KEY] = language_code
        
        return Response({
            'success': True,
            'message': _('Language changed successfully'),
            'language': language_code,
            'language_name': dict(settings.LANGUAGES).get(language_code, language_code)
        })
        
    except json.JSONDecodeError:
        return Response({
            'error': _('Invalid JSON data')
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def get_translations(request):
    """Get common translations for frontend"""
    
    # Common translations that frontend needs
    translations = {
        # Navigation
        'dashboard': _('Dashboard'),
        'citizens': _('Citizens'),
        'id_cards': _('ID Cards'),
        'clearance': _('Clearance'),
        'transfer': _('Transfer'),
        'print_queue': _('Print Queue'),
        'service_requests': _('Service Requests'),
        'kebele_users': _('Kebele Users'),
        'reports': _('Reports'),
        'citizen_directory': _('Citizen Directory'),
        'subcity_users': _('Subcity Users'),
        'tenants': _('Tenants'),
        'system_users': _('System Users'),
        'system_settings': _('System Settings'),
        
        # Authentication
        'login': _('Login'),
        'logout': _('Logout'),
        'username': _('Username'),
        'password': _('Password'),
        'email': _('Email'),
        'first_name': _('First Name'),
        'last_name': _('Last Name'),
        
        # Citizen Management
        'add_citizen': _('Add Citizen'),
        'edit_citizen': _('Edit Citizen'),
        'delete_citizen': _('Delete Citizen'),
        'citizen_registration': _('Citizen Registration'),
        'full_name': _('Full Name'),
        'date_of_birth': _('Date of Birth'),
        'gender': _('Gender'),
        'male': _('Male'),
        'female': _('Female'),
        'phone_number': _('Phone Number'),
        'address': _('Address'),
        'nationality': _('Nationality'),
        'ethiopian': _('Ethiopian'),
        
        # ID Card Management
        'generate_id_card': _('Generate ID Card'),
        'print_id_card': _('Print ID Card'),
        'id_card_status': _('ID Card Status'),
        'pending': _('Pending'),
        'approved': _('Approved'),
        'rejected': _('Rejected'),
        'printed': _('Printed'),
        
        # Biometric Capture
        'capture_fingerprint': _('Capture Fingerprint'),
        'left_thumb': _('Left Thumb'),
        'right_thumb': _('Right Thumb'),
        'fingerprint_quality': _('Fingerprint Quality'),
        'high_quality': _('High Quality'),
        'medium_quality': _('Medium Quality'),
        'low_quality': _('Low Quality'),
        'biometric_data': _('Biometric Data'),
        'duplicate_detection': _('Duplicate Detection'),
        'no_duplicates_found': _('No Duplicates Found'),
        'duplicate_found': _('Duplicate Found'),
        
        # Common Actions
        'save': _('Save'),
        'cancel': _('Cancel'),
        'submit': _('Submit'),
        'edit': _('Edit'),
        'delete': _('Delete'),
        'view': _('View'),
        'search': _('Search'),
        'filter': _('Filter'),
        'export': _('Export'),
        'import': _('Import'),
        'print': _('Print'),
        
        # Status Messages
        'success': _('Success'),
        'error': _('Error'),
        'warning': _('Warning'),
        'information': _('Information'),
        'loading': _('Loading...'),
        'please_wait': _('Please wait...'),
        
        # Form Validation
        'field_required': _('This field is required.'),
        'invalid_email': _('Please enter a valid email address.'),
        'invalid_phone': _('Please enter a valid phone number.'),
        'password_min_length': _('Password must be at least 8 characters long.'),
        
        # Date and Time
        'today': _('Today'),
        'yesterday': _('Yesterday'),
        'tomorrow': _('Tomorrow'),
        'date': _('Date'),
        'time': _('Time'),
        
        # Numbers and Quantities
        'total': _('Total'),
        'count': _('Count'),
        'page': _('Page'),
        'of': _('of'),
        'items_per_page': _('items per page'),
        
        # System Messages
        'system_ready': _('System is ready'),
        'connection_established': _('Connection established'),
        'connection_failed': _('Connection failed'),
        'device_connected': _('Device connected'),
        'device_not_found': _('Device not found'),
        
        # Workflow States
        'draft': _('Draft'),
        'under_review': _('Under Review'),
        'completed': _('Completed'),
        'cancelled': _('Cancelled'),
        
        # Regional/Administrative
        'city': _('City'),
        'subcity': _('Subcity'),
        'kebele': _('Kebele'),
        'woreda': _('Woreda'),
        'zone': _('Zone'),
        'region': _('Region'),
        
        # File Operations
        'upload': _('Upload'),
        'download': _('Download'),
        'file': _('File'),
        'image': _('Image'),
        'document': _('Document'),
        'photo': _('Photo'),
        
        # Permissions and Roles
        'administrator': _('Administrator'),
        'user': _('User'),
        'clerk': _('Clerk'),
        'leader': _('Leader'),
        'manager': _('Manager'),
        'supervisor': _('Supervisor'),
        
        # Settings and Configuration
        'settings': _('Settings'),
        'configuration': _('Configuration'),
        'preferences': _('Preferences'),
        'language': _('Language'),
        'theme': _('Theme'),
        'notifications': _('Notifications'),
    }
    
    return Response({
        'translations': translations,
        'language': translation.get_language(),
        'language_name': dict(settings.LANGUAGES).get(translation.get_language(), 'English')
    })


@api_view(['GET'])
def get_language_info(request):
    """Get current language information"""
    
    current_language = translation.get_language()
    language_name = dict(settings.LANGUAGES).get(current_language, 'English')
    
    return Response({
        'current_language': current_language,
        'language_name': language_name,
        'available_languages': [
            {
                'code': code,
                'name': name,
                'is_current': code == current_language
            }
            for code, name in settings.LANGUAGES
        ],
        'is_rtl': current_language in ['ar', 'he', 'fa'],  # Right-to-left languages
        'date_format': 'DD/MM/YYYY' if current_language == 'am' else 'MM/DD/YYYY'
    })
