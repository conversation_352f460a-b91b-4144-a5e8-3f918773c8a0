"""
Test script for Futronic ANSI/ISO SDK Service
"""

import requests
import json
import time

def test_ansi_service():
    """Test the Futronic ANSI/ISO SDK service"""
    
    base_url = "http://localhost:8002"
    
    print("🧪 Testing Futronic ANSI/ISO SDK Service")
    print("=" * 50)
    
    # Test 1: Health check
    print("\n1. Testing health check...")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print("✅ Health check passed")
            print(f"   SDK Initialized: {health_data.get('sdk_initialized')}")
            print(f"   Standards: {health_data.get('standards')}")
            print(f"   NIST Certified: {health_data.get('nist_certified')}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False
    
    # Test 2: Fingerprint capture
    print("\n2. Testing fingerprint capture...")
    print("   📋 Please place your finger on the scanner...")
    
    try:
        response = requests.post(f"{base_url}/capture", timeout=30)
        if response.status_code == 200:
            capture_data = response.json()
            if capture_data.get('success'):
                template = capture_data.get('template', {})
                print("✅ Fingerprint capture successful")
                print(f"   Template Size: {template.get('template_size')} bytes")
                print(f"   Minutiae Count: {template.get('minutiae_count')}")
                print(f"   Quality Score: {template.get('quality_score')}%")
                print(f"   Standard: {template.get('standard')}")
                print(f"   NIST Certified: {template.get('nist_certified')}")
                
                # Store template for matching test
                global captured_template
                captured_template = template.get('template_data')
                
            else:
                print(f"❌ Capture failed: {capture_data.get('error')}")
                return False
        else:
            print(f"❌ Capture request failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Capture error: {e}")
        return False
    
    # Test 3: Template matching (self-match)
    print("\n3. Testing template matching...")
    print("   📋 Please place the SAME finger on the scanner again...")
    
    try:
        # Capture second template
        response = requests.post(f"{base_url}/capture", timeout=30)
        if response.status_code == 200:
            capture_data = response.json()
            if capture_data.get('success'):
                template2 = capture_data.get('template', {}).get('template_data')
                
                # Match templates
                match_data = {
                    'template1': captured_template,
                    'template2': template2
                }
                
                response = requests.post(f"{base_url}/match", json=match_data, timeout=10)
                if response.status_code == 200:
                    match_result = response.json()
                    if match_result.get('success'):
                        print("✅ Template matching successful")
                        print(f"   Match Score: {match_result.get('match_score'):.3f}")
                        print(f"   Is Match: {match_result.get('is_match')}")
                        print(f"   Threshold: {match_result.get('threshold')}")
                        print(f"   NIST Certified: {match_result.get('nist_certified')}")
                    else:
                        print(f"❌ Matching failed: {match_result.get('error')}")
                        return False
                else:
                    print(f"❌ Match request failed: {response.status_code}")
                    return False
            else:
                print(f"❌ Second capture failed: {capture_data.get('error')}")
                return False
        else:
            print(f"❌ Second capture request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Matching error: {e}")
        return False
    
    print("\n🎉 All tests completed successfully!")
    print("✅ Futronic ANSI/ISO SDK Service is working properly")
    return True

if __name__ == "__main__":
    print("🚀 Starting Futronic ANSI/ISO SDK Service Test")
    print("📋 Make sure the service is running on port 8002")
    print("📋 Make sure your Futronic fingerprint scanner is connected")
    
    input("\nPress Enter to start testing...")
    
    success = test_ansi_service()
    
    if success:
        print("\n🎯 Test Summary: ALL TESTS PASSED")
        print("   The Futronic ANSI/ISO SDK service is ready for integration")
    else:
        print("\n❌ Test Summary: SOME TESTS FAILED")
        print("   Please check the service logs and device connection")
