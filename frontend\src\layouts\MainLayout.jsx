import { Outlet, useLocation } from 'react-router-dom';
import { Box, CssBaseline, Container, Toolbar } from '@mui/material';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Person as PersonIcon,
  CreditCard as CardIcon,
  Business as BusinessIcon,
  Assessment as ReportIcon,
  AccountCircle as ProfileIcon,
  Apps as DefaultIcon
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import Navbar from '../components/navigation/Navbar';
import DynamicNavbar from '../components/navigation/DynamicNavbar';
import Banner from '../components/common/Banner';
import Footer from '../components/common/Footer';

const MainLayout = () => {
  const { user } = useAuth();
  const location = useLocation();

  // Get page title based on current route
  const getPageInfo = () => {
    const path = location.pathname;

    if (path === '/dashboard') {
      return {
        title: 'Dashboard',
        subtitle: 'Welcome to your ID management dashboard',
        icon: <DashboardIcon fontSize="inherit" />
      };
    } else if (path.startsWith('/citizens') || path.includes('/citizens')) {
      // Check if it's cross-tenant citizen viewing (subcity viewing kebele citizens)
      if (path.includes('/tenants/') && path.includes('/citizens')) {
        return {
          title: 'Citizens Overview',
          subtitle: 'View citizen information from kebele tenants',
          icon: <PersonIcon fontSize="inherit" />
        };
      } else {
        return {
          title: 'Citizens Management',
          subtitle: 'Register and manage citizen information',
          icon: <PersonIcon fontSize="inherit" />
        };
      }
    } else if (path.includes('/printing-queue')) {
      return {
        title: 'ID Card Printing Queue',
        subtitle: 'Print approved ID cards',
        icon: <CardIcon fontSize="inherit" />
      };
    } else if (path.startsWith('/idcards') || path.includes('/idcards')) {
      if (path.includes('/services/idcard') || path.includes('idcards/services')) {
        return {
          title: 'ID Card Services',
          subtitle: 'Apply for ID card renewal, replacement, and reprint services',
          icon: <CardIcon fontSize="inherit" />
        };
      } else {
        return {
          title: 'ID Cards',
          subtitle: 'Create and manage digital ID cards',
          icon: <CardIcon fontSize="inherit" />
        };
      }
    } else if (path.startsWith('/services/idcard')) {
      return {
        title: 'ID Card Services',
        subtitle: 'Apply for ID card renewal, replacement, and reprint services',
        icon: <CardIcon fontSize="inherit" />
      };
    } else if (path.startsWith('/reports')) {
      return {
        title: 'Reports',
        subtitle: 'View and generate system reports',
        icon: <ReportIcon fontSize="inherit" />
      };
    } else if (path.startsWith('/users')) {
      return {
        title: 'User Management',
        subtitle: 'Manage system users and permissions',
        icon: <PeopleIcon fontSize="inherit" />
      };
    } else if (path.startsWith('/tenants')) {
      return {
        title: 'Tenant Management',
        subtitle: 'Manage organizational hierarchy',
        icon: <BusinessIcon fontSize="inherit" />
      };
    } else if (path.startsWith('/profile')) {
      return {
        title: 'My Profile',
        subtitle: 'View and update your profile information',
        icon: <ProfileIcon fontSize="inherit" />
      };
    } else {
      return {
        title: 'GoID System',
        subtitle: 'Digital ID Card Management',
        icon: <DefaultIcon fontSize="inherit" />
      };
    }
  };

  const { title, subtitle, icon } = getPageInfo();

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      <CssBaseline />
      <DynamicNavbar />
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: '100%',
        }}
      >
        {/* Banner with no spacing from navbar - full width */}
        <Box
          sx={{
            mt: { xs: 7, sm: 8 }, // Adjusted to connect with navbar
            mb: 3,
            position: 'relative',
          }}
        >
          <Banner title={title} subtitle={subtitle} icon={icon} />
        </Box>

        {/* Main content */}
        <Container maxWidth="xl" sx={{ py: 3 }}>
          <Outlet />
        </Container>
      </Box>
      <Footer />
    </Box>
  );
};

export default MainLayout;
