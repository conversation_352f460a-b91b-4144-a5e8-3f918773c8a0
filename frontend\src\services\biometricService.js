import axios from '../utils/axios';

class BiometricService {
  constructor() {
    this.baseURL = '/api/tenants/biometrics';
    this.useLocalDevice = false; // Will be set based on device detection
    this.biometricServiceUrl = null; // Will be set when device is found
  }

  /**
   * Get possible biometric service URLs to try
   * @returns {Array} Array of URLs to try
   */
  _getBiometricServiceUrls() {
    // Check for environment variable first (for production deployments)
    const envUrl = import.meta.env.VITE_BIOMETRIC_SERVICE_URL;
    if (envUrl) {
      console.log(`🔧 Using configured biometric service URL: ${envUrl}`);
      return [envUrl];
    }

    // Default URLs to try (in order of preference)
    return [
      'http://localhost:8001',           // Same machine (kebele workstation)
      'http://host.docker.internal:8001', // Docker Desktop host access
      'http://**********:8001',          // Docker bridge IP
      'http://*************:8001',       // Common local network IP (adjust as needed)
      'http://*************:8001',       // Another common IP
      'http://*************:8001',       // Another common IP
    ];
  }

  /**
   * Create biometric record for a citizen
   * @param {Object} biometricData - Biometric data including fingerprints
   * @returns {Promise} API response
   */
  async createBiometric(biometricData) {
    try {
      const response = await axios.post(`${this.baseURL}/`, biometricData);
      return response.data;
    } catch (error) {
      console.error('Error creating biometric record:', error);
      throw error;
    }
  }

  /**
   * Update biometric record
   * @param {number} biometricId - Biometric record ID
   * @param {Object} biometricData - Updated biometric data
   * @returns {Promise} API response
   */
  async updateBiometric(biometricId, biometricData) {
    try {
      const response = await axios.put(`${this.baseURL}/${biometricId}/`, biometricData);
      return response.data;
    } catch (error) {
      console.error('Error updating biometric record:', error);
      throw error;
    }
  }

  /**
   * Get biometric record by citizen ID
   * @param {number} citizenId - Citizen ID
   * @returns {Promise} API response
   */
  async getBiometricByCitizen(citizenId) {
    try {
      const response = await axios.get(`${this.baseURL}/?citizen=${citizenId}`);
      return response.data.results?.[0] || null;
    } catch (error) {
      console.error('Error fetching biometric record:', error);
      throw error;
    }
  }

  /**
   * Validate fingerprint quality
   * @param {string} fingerprintData - Base64 encoded fingerprint template
   * @returns {Promise} Validation result
   */
  async validateFingerprintQuality(fingerprintData) {
    try {
      const response = await axios.post('/api/biometrics/validate-quality/', {
        fingerprint_data: fingerprintData
      });
      return response.data;
    } catch (error) {
      console.error('Error validating fingerprint quality:', error);
      throw error;
    }
  }

  /**
   * Check for duplicate fingerprints across tenants
   * @param {Object} fingerprintData - Fingerprint data to check
   * @returns {Promise} Duplicate check result
   */
  async checkDuplicateFingerprints(fingerprintData) {
    try {
      const response = await axios.post('/api/biometrics/check-duplicates/', fingerprintData);
      return response.data;
    } catch (error) {
      console.error('Error checking duplicate fingerprints:', error);
      throw error;
    }
  }

  /**
   * Get device status - Try multiple biometric service locations
   * @returns {Promise} Device status
   */
  async getDeviceStatus() {
    // Get biometric service URL from environment or try multiple locations
    const biometricServiceUrls = this._getBiometricServiceUrls();

    for (const serviceUrl of biometricServiceUrls) {
      try {
        console.log(`🔍 Checking biometric service at: ${serviceUrl}`);
        const response = await fetch(`${serviceUrl}/api/device/status`, {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          signal: AbortSignal.timeout(3000) // 3 second timeout per URL
        });

        if (response.ok) {
          const data = await response.json();
          console.log(`✅ Using biometric device at: ${serviceUrl}`);
          this.biometricServiceUrl = serviceUrl;
          this.useLocalDevice = true;
          return data;
        } else {
          console.log(`❌ Service at ${serviceUrl} returned status: ${response.status}`);
        }
      } catch (error) {
        console.log(`❌ Service at ${serviceUrl} failed: ${error.message}`);
        continue; // Try next URL
      }
    }

    // If we get here, no biometric service was found
    console.error('❌ No biometric device service found at any location');
    throw new Error('Biometric device service not available. Please ensure the biometric service is running on the machine with the fingerprint device.');
  }

  /**
   * Initialize fingerprint device - Use discovered biometric service
   * @returns {Promise} Initialization result
   */
  async initializeDevice() {
    if (!this.useLocalDevice || !this.biometricServiceUrl) {
      throw new Error('Biometric device service not configured. Please run device status check first.');
    }

    try {
      console.log(`🔧 Initializing device at: ${this.biometricServiceUrl}`);
      const response = await fetch(`${this.biometricServiceUrl}/api/device/initialize`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        signal: AbortSignal.timeout(5000)
      });

      if (response.ok) {
        const data = await response.json();
        console.log(`✅ Device initialized at: ${this.biometricServiceUrl}`);
        return data;
      } else {
        throw new Error(`Device initialization failed: ${response.status}`);
      }
    } catch (error) {
      console.error(`❌ Device initialization failed at ${this.biometricServiceUrl}:`, error.message);
      throw new Error(`Device initialization failed: ${error.message}`);
    }
  }

  /**
   * Capture fingerprint from device - Use discovered biometric service
   * @param {string} thumbType - 'left' or 'right'
   * @returns {Promise} Capture result
   */
  async captureFingerprint(thumbType) {
    if (!this.useLocalDevice || !this.biometricServiceUrl) {
      throw new Error('Biometric device service not configured. Please run device status check first.');
    }

    try {
      console.log(`📸 Capturing ${thumbType} thumb at: ${this.biometricServiceUrl}`);
      const response = await fetch(`${this.biometricServiceUrl}/api/capture/fingerprint`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ thumb_type: thumbType }),
        signal: AbortSignal.timeout(30000) // 30 second timeout for capture
      });

      if (response.ok) {
        const data = await response.json();
        console.log(`✅ ${thumbType} thumb captured via biometric service at: ${this.biometricServiceUrl}`);
        return data;
      } else {
        throw new Error(`Biometric capture failed: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.error(`❌ Biometric capture failed for ${thumbType} thumb at ${this.biometricServiceUrl}:`, error.message);
      throw new Error(`Biometric capture failed for ${thumbType} thumb: ${error.message}`);
    }
  }

  /**
   * Process raw fingerprint data into template
   * @param {string} rawData - Raw fingerprint data
   * @param {string} thumbType - 'left' or 'right'
   * @returns {Promise} Processing result
   */
  async processFingerprint(rawData, thumbType) {
    try {
      const response = await axios.post('/api/biometrics/process/', {
        raw_data: rawData,
        thumb_type: thumbType
      });
      return response.data;
    } catch (error) {
      console.error('Error processing fingerprint:', error);
      throw error;
    }
  }

  /**
   * Match fingerprints for verification
   * @param {string} template1 - First fingerprint template
   * @param {string} template2 - Second fingerprint template
   * @returns {Promise} Match result
   */
  async matchFingerprints(template1, template2) {
    try {
      const response = await axios.post('/api/biometrics/match/', {
        template1,
        template2
      });
      return response.data;
    } catch (error) {
      console.error('Error matching fingerprints:', error);
      throw error;
    }
  }

  /**
   * Get biometric statistics
   * @returns {Promise} Statistics data
   */
  async getBiometricStats() {
    try {
      const response = await axios.get('/api/biometrics/stats/');
      return response.data;
    } catch (error) {
      console.error('Error fetching biometric statistics:', error);
      throw error;
    }
  }

  /**
   * Export biometric data (for backup/migration)
   * @param {Array} citizenIds - Array of citizen IDs to export
   * @returns {Promise} Export data
   */
  async exportBiometricData(citizenIds = []) {
    try {
      const response = await axios.post('/api/biometrics/export/', {
        citizen_ids: citizenIds
      });
      return response.data;
    } catch (error) {
      console.error('Error exporting biometric data:', error);
      throw error;
    }
  }

  /**
   * Import biometric data (for backup/migration)
   * @param {Object} biometricData - Biometric data to import
   * @returns {Promise} Import result
   */
  async importBiometricData(biometricData) {
    try {
      const response = await axios.post('/api/biometrics/import/', biometricData);
      return response.data;
    } catch (error) {
      console.error('Error importing biometric data:', error);
      throw error;
    }
  }

  /**
   * Delete biometric record
   * @param {number} biometricId - Biometric record ID
   * @returns {Promise} Deletion result
   */
  async deleteBiometric(biometricId) {
    try {
      const response = await axios.delete(`${this.baseURL}/${biometricId}/`);
      return response.data;
    } catch (error) {
      console.error('Error deleting biometric record:', error);
      throw error;
    }
  }

  /**
   * Get supported biometric types
   * @returns {Promise} Supported types
   */
  async getSupportedTypes() {
    try {
      const response = await axios.get('/api/shared/biometric-types/');
      return response.data;
    } catch (error) {
      console.error('Error fetching biometric types:', error);
      throw error;
    }
  }

  /**
   * Comprehensive fraud prevention check
   * @param {Object} citizenData - Citizen personal information
   * @param {Object} biometricData - Biometric data (fingerprints)
   * @returns {Promise} Fraud check results
   */
  async fraudPreventionCheck(citizenData, biometricData) {
    try {
      const response = await axios.post('/api/biometrics/fraud-prevention/check/', {
        citizen_data: citizenData,
        biometric_data: biometricData
      });
      return response.data;
    } catch (error) {
      console.error('Error in fraud prevention check:', error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const biometricService = new BiometricService();
export default biometricService;
